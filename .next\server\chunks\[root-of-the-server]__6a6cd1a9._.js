module.exports = {

"[project]/.next-internal/server/app/api/tickets/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/pg [external] (pg, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("pg");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
// Database configuration and operations for AIpariat
__turbopack_context__.s({
    "cleanupExpiredTickets": (()=>cleanupExpiredTickets),
    "closeDatabase": (()=>closeDatabase),
    "getTicket": (()=>getTicket),
    "getTicketStats": (()=>getTicketStats),
    "initializeDatabase": (()=>initializeDatabase),
    "saveTicket": (()=>saveTicket)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
// Database connection
let pool = null;
function getPool() {
    if (!pool) {
        pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__["Pool"]({
            connectionString: process.env.DATABASE_URL,
            ssl: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : false
        });
    }
    return pool;
}
async function initializeDatabase() {
    const client = getPool();
    try {
        // Create tickets table if it doesn't exist
        await client.query(`
      CREATE TABLE IF NOT EXISTS shared_tickets (
        id VARCHAR(255) PRIMARY KEY,
        ticket_data JSONB NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL
      );
    `);
        // Create index for cleanup
        await client.query(`
      CREATE INDEX IF NOT EXISTS idx_shared_tickets_expires_at 
      ON shared_tickets(expires_at);
    `);
        console.log('Database initialized successfully');
    } catch (error) {
        console.error('Error initializing database:', error);
        throw error;
    }
}
async function saveTicket(ticketId, ticket) {
    const client = getPool();
    try {
        const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now
        await client.query(`INSERT INTO shared_tickets (id, ticket_data, expires_at) 
       VALUES ($1, $2, $3)`, [
            ticketId,
            JSON.stringify(ticket),
            expiresAt
        ]);
        return ticketId;
    } catch (error) {
        console.error('Error saving ticket:', error);
        throw error;
    }
}
async function getTicket(ticketId) {
    const client = getPool();
    try {
        const result = await client.query(`SELECT id, ticket_data, created_at, expires_at 
       FROM shared_tickets 
       WHERE id = $1 AND expires_at > NOW()`, [
            ticketId
        ]);
        if (result.rows.length === 0) {
            return null;
        }
        const row = result.rows[0];
        return {
            id: row.id,
            ticket: typeof row.ticket_data === 'string' ? JSON.parse(row.ticket_data) : row.ticket_data,
            createdAt: row.created_at.toISOString(),
            expiresAt: row.expires_at.toISOString()
        };
    } catch (error) {
        console.error('Error getting ticket:', error);
        throw error;
    }
}
async function cleanupExpiredTickets() {
    const client = getPool();
    try {
        const result = await client.query(`DELETE FROM shared_tickets WHERE expires_at <= NOW()`);
        console.log(`Cleaned up ${result.rowCount} expired tickets`);
        return result.rowCount || 0;
    } catch (error) {
        console.error('Error cleaning up expired tickets:', error);
        throw error;
    }
}
async function getTicketStats() {
    const client = getPool();
    try {
        const totalResult = await client.query(`SELECT COUNT(*) as count FROM shared_tickets WHERE expires_at > NOW()`);
        const todayResult = await client.query(`SELECT COUNT(*) as count FROM shared_tickets 
       WHERE created_at >= CURRENT_DATE AND expires_at > NOW()`);
        const weekResult = await client.query(`SELECT COUNT(*) as count FROM shared_tickets 
       WHERE created_at >= CURRENT_DATE - INTERVAL '7 days' AND expires_at > NOW()`);
        return {
            total: parseInt(totalResult.rows[0].count),
            todayCount: parseInt(todayResult.rows[0].count),
            weekCount: parseInt(weekResult.rows[0].count)
        };
    } catch (error) {
        console.error('Error getting ticket stats:', error);
        throw error;
    }
}
async function closeDatabase() {
    if (pool) {
        await pool.end();
        pool = null;
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/app/api/tickets/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
// API route for creating shared tickets
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
// Initialize database on first request
let dbInitialized = false;
async function ensureDbInitialized() {
    if (!dbInitialized) {
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["initializeDatabase"])();
            dbInitialized = true;
        } catch (error) {
            console.error('Failed to initialize database:', error);
            throw error;
        }
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { ticket } = body;
        if (!ticket || !ticket.id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid ticket data'
            }, {
                status: 400
            });
        }
        // Generate unique ticket ID for sharing
        const shareId = `ticket_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        // Try to save to database, but don't fail if it doesn't work
        try {
            await ensureDbInitialized();
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["saveTicket"])(shareId, ticket);
            console.log('Ticket saved to database successfully');
        } catch (dbError) {
            console.warn('Database save failed, ticket will be stored client-side only:', dbError);
        // Continue without database - the ticket will be stored in localStorage on client
        }
        // Return the share URL
        const baseUrl = process.env.BASE_URL || (request.headers.get('host') ? `${request.headers.get('x-forwarded-proto') || 'http'}://${request.headers.get('host')}` : 'http://localhost:3000');
        const shareUrl = `${baseUrl}/bilet/${shareId}`;
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            shareId,
            shareUrl
        });
    } catch (error) {
        console.error('Error creating shared ticket:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to create shared ticket'
        }, {
            status: 500
        });
    }
}
async function GET() {
    try {
        await ensureDbInitialized();
        // You can add statistics here if needed
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            status: 'API is working',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error in tickets API:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'API error'
        }, {
            status: 500
        });
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__6a6cd1a9._.js.map