// API route for creating shared tickets
import { NextRequest, NextResponse } from 'next/server';
import { saveTicket, initializeDatabase } from '@/lib/database';
import { Ticket } from '@/lib/ticketGenerator';

// Initialize database on first request
let dbInitialized = false;

async function ensureDbInitialized() {
  if (!dbInitialized) {
    try {
      await initializeDatabase();
      dbInitialized = true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { ticket }: { ticket: Ticket } = body;

    if (!ticket || !ticket.id) {
      return NextResponse.json(
        { error: 'Invalid ticket data' },
        { status: 400 }
      );
    }

    // Generate unique ticket ID for sharing
    const shareId = `ticket_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Try to save to database, but don't fail if it doesn't work
    try {
      await ensureDbInitialized();
      await saveTicket(shareId, ticket);
      console.log('Ticket saved to database successfully');
    } catch (dbError) {
      console.warn('Database save failed, ticket will be stored client-side only:', dbError);
      // Continue without database - the ticket will be stored in localStorage on client
    }

    // Return the share URL
    const baseUrl = process.env.BASE_URL ||
                   (request.headers.get('host') ?
                    `${request.headers.get('x-forwarded-proto') || 'http'}://${request.headers.get('host')}` :
                    'http://localhost:3000');

    const shareUrl = `${baseUrl}/bilet/${shareId}`;

    return NextResponse.json({
      success: true,
      shareId,
      shareUrl,
    });

  } catch (error) {
    console.error('Error creating shared ticket:', error);
    return NextResponse.json(
      { error: 'Failed to create shared ticket' },
      { status: 500 }
    );
  }
}

// Optional: GET endpoint for statistics (admin use)
export async function GET() {
  try {
    await ensureDbInitialized();
    
    // You can add statistics here if needed
    return NextResponse.json({
      status: 'API is working',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in tickets API:', error);
    return NextResponse.json(
      { error: 'API error' },
      { status: 500 }
    );
  }
}
