{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/src/lib/database.ts"], "sourcesContent": ["// Database configuration and operations for AIpariat\r\nimport { Pool } from 'pg';\r\nimport { Ticket } from './ticketGenerator';\r\n\r\ninterface TicketData {\r\n  id: string;\r\n  ticket: Ticket;\r\n  createdAt: string;\r\n  expiresAt: string;\r\n}\r\n\r\n// Database connection\r\nlet pool: Pool | null = null;\r\n\r\nfunction getPool(): Pool {\r\n  if (!pool) {\r\n    pool = new Pool({\r\n      connectionString: process.env.DATABASE_URL,\r\n      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,\r\n    });\r\n  }\r\n  return pool;\r\n}\r\n\r\n// Initialize database tables\r\nexport async function initializeDatabase(): Promise<void> {\r\n  const client = getPool();\r\n  \r\n  try {\r\n    // Create tickets table if it doesn't exist\r\n    await client.query(`\r\n      CREATE TABLE IF NOT EXISTS shared_tickets (\r\n        id VARCHAR(255) PRIMARY KEY,\r\n        ticket_data JSONB NOT NULL,\r\n        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\r\n        expires_at TIMESTAMP WITH TIME ZONE NOT NULL\r\n      );\r\n    `);\r\n\r\n    // Create index for cleanup\r\n    await client.query(`\r\n      CREATE INDEX IF NOT EXISTS idx_shared_tickets_expires_at \r\n      ON shared_tickets(expires_at);\r\n    `);\r\n\r\n    console.log('Database initialized successfully');\r\n  } catch (error) {\r\n    console.error('Error initializing database:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Save a ticket to database\r\nexport async function saveTicket(ticketId: string, ticket: Ticket): Promise<string> {\r\n  const client = getPool();\r\n  \r\n  try {\r\n    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now\r\n    \r\n    await client.query(\r\n      `INSERT INTO shared_tickets (id, ticket_data, expires_at) \r\n       VALUES ($1, $2, $3)`,\r\n      [ticketId, JSON.stringify(ticket), expiresAt]\r\n    );\r\n\r\n    return ticketId;\r\n  } catch (error) {\r\n    console.error('Error saving ticket:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Get a ticket from database\r\nexport async function getTicket(ticketId: string): Promise<TicketData | null> {\r\n  const client = getPool();\r\n  \r\n  try {\r\n    const result = await client.query(\r\n      `SELECT id, ticket_data, created_at, expires_at \r\n       FROM shared_tickets \r\n       WHERE id = $1 AND expires_at > NOW()`,\r\n      [ticketId]\r\n    );\r\n\r\n    if (result.rows.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    const row = result.rows[0];\r\n    return {\r\n      id: row.id,\r\n      ticket: typeof row.ticket_data === 'string' ? JSON.parse(row.ticket_data) : row.ticket_data,\r\n      createdAt: row.created_at.toISOString(),\r\n      expiresAt: row.expires_at.toISOString(),\r\n    };\r\n  } catch (error) {\r\n    console.error('Error getting ticket:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Clean up expired tickets (called periodically)\r\nexport async function cleanupExpiredTickets(): Promise<number> {\r\n  const client = getPool();\r\n  \r\n  try {\r\n    const result = await client.query(\r\n      `DELETE FROM shared_tickets WHERE expires_at <= NOW()`\r\n    );\r\n\r\n    console.log(`Cleaned up ${result.rowCount} expired tickets`);\r\n    return result.rowCount || 0;\r\n  } catch (error) {\r\n    console.error('Error cleaning up expired tickets:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Get statistics (for admin/monitoring)\r\nexport async function getTicketStats(): Promise<{\r\n  total: number;\r\n  todayCount: number;\r\n  weekCount: number;\r\n}> {\r\n  const client = getPool();\r\n  \r\n  try {\r\n    const totalResult = await client.query(\r\n      `SELECT COUNT(*) as count FROM shared_tickets WHERE expires_at > NOW()`\r\n    );\r\n\r\n    const todayResult = await client.query(\r\n      `SELECT COUNT(*) as count FROM shared_tickets \r\n       WHERE created_at >= CURRENT_DATE AND expires_at > NOW()`\r\n    );\r\n\r\n    const weekResult = await client.query(\r\n      `SELECT COUNT(*) as count FROM shared_tickets \r\n       WHERE created_at >= CURRENT_DATE - INTERVAL '7 days' AND expires_at > NOW()`\r\n    );\r\n\r\n    return {\r\n      total: parseInt(totalResult.rows[0].count),\r\n      todayCount: parseInt(todayResult.rows[0].count),\r\n      weekCount: parseInt(weekResult.rows[0].count),\r\n    };\r\n  } catch (error) {\r\n    console.error('Error getting ticket stats:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\n// Close database connection (for cleanup)\r\nexport async function closeDatabase(): Promise<void> {\r\n  if (pool) {\r\n    await pool.end();\r\n    pool = null;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;AACrD;;;;;;AAUA,sBAAsB;AACtB,IAAI,OAAoB;AAExB,SAAS;IACP,IAAI,CAAC,MAAM;QACT,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;YACd,kBAAkB,QAAQ,GAAG,CAAC,YAAY;YAC1C,KAAK,6EAAwE;QAC/E;IACF;IACA,OAAO;AACT;AAGO,eAAe;IACpB,MAAM,SAAS;IAEf,IAAI;QACF,2CAA2C;QAC3C,MAAM,OAAO,KAAK,CAAC,CAAC;;;;;;;IAOpB,CAAC;QAED,2BAA2B;QAC3B,MAAM,OAAO,KAAK,CAAC,CAAC;;;IAGpB,CAAC;QAED,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,eAAe,WAAW,QAAgB,EAAE,MAAc;IAC/D,MAAM,SAAS;IAEf,IAAI;QACF,MAAM,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,OAAO,kBAAkB;QAEpF,MAAM,OAAO,KAAK,CAChB,CAAC;0BACmB,CAAC,EACrB;YAAC;YAAU,KAAK,SAAS,CAAC;YAAS;SAAU;QAG/C,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,MAAM;IACR;AACF;AAGO,eAAe,UAAU,QAAgB;IAC9C,MAAM,SAAS;IAEf,IAAI;QACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,CAAC;;2CAEoC,CAAC,EACtC;YAAC;SAAS;QAGZ,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG;YAC5B,OAAO;QACT;QAEA,MAAM,MAAM,OAAO,IAAI,CAAC,EAAE;QAC1B,OAAO;YACL,IAAI,IAAI,EAAE;YACV,QAAQ,OAAO,IAAI,WAAW,KAAK,WAAW,KAAK,KAAK,CAAC,IAAI,WAAW,IAAI,IAAI,WAAW;YAC3F,WAAW,IAAI,UAAU,CAAC,WAAW;YACrC,WAAW,IAAI,UAAU,CAAC,WAAW;QACvC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAGO,eAAe;IACpB,MAAM,SAAS;IAEf,IAAI;QACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,CAAC,oDAAoD,CAAC;QAGxD,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,QAAQ,CAAC,gBAAgB,CAAC;QAC3D,OAAO,OAAO,QAAQ,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM;IACR;AACF;AAGO,eAAe;IAKpB,MAAM,SAAS;IAEf,IAAI;QACF,MAAM,cAAc,MAAM,OAAO,KAAK,CACpC,CAAC,qEAAqE,CAAC;QAGzE,MAAM,cAAc,MAAM,OAAO,KAAK,CACpC,CAAC;8DACuD,CAAC;QAG3D,MAAM,aAAa,MAAM,OAAO,KAAK,CACnC,CAAC;kFAC2E,CAAC;QAG/E,OAAO;YACL,OAAO,SAAS,YAAY,IAAI,CAAC,EAAE,CAAC,KAAK;YACzC,YAAY,SAAS,YAAY,IAAI,CAAC,EAAE,CAAC,KAAK;YAC9C,WAAW,SAAS,WAAW,IAAI,CAAC,EAAE,CAAC,KAAK;QAC9C;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,eAAe;IACpB,IAAI,MAAM;QACR,MAAM,KAAK,GAAG;QACd,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/src/app/api/tickets/route.ts"], "sourcesContent": ["// API route for creating shared tickets\r\nimport { NextRequest, NextResponse } from 'next/server';\r\nimport { saveTicket, initializeDatabase } from '@/lib/database';\r\nimport { Ticket } from '@/lib/ticketGenerator';\r\n\r\n// Initialize database on first request\r\nlet dbInitialized = false;\r\n\r\nasync function ensureDbInitialized() {\r\n  if (!dbInitialized) {\r\n    try {\r\n      await initializeDatabase();\r\n      dbInitialized = true;\r\n    } catch (error) {\r\n      console.error('Failed to initialize database:', error);\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n\r\nexport async function POST(request: NextRequest) {\r\n  try {\r\n    await ensureDbInitialized();\r\n\r\n    const body = await request.json();\r\n    const { ticket }: { ticket: Ticket } = body;\r\n\r\n    if (!ticket || !ticket.id) {\r\n      return NextResponse.json(\r\n        { error: 'Invalid ticket data' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    // Generate unique ticket ID for sharing\r\n    const shareId = `ticket_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n    \r\n    // Save to database\r\n    await saveTicket(shareId, ticket);\r\n\r\n    // Return the share URL\r\n    const baseUrl = process.env.BASE_URL ||\r\n                   (request.headers.get('host') ?\r\n                    `${request.headers.get('x-forwarded-proto') || 'https'}://${request.headers.get('host')}` :\r\n                    'http://localhost:3000');\r\n    \r\n    const shareUrl = `${baseUrl}/bilet/${shareId}`;\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      shareId,\r\n      shareUrl,\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('Error creating shared ticket:', error);\r\n    return NextResponse.json(\r\n      { error: 'Failed to create shared ticket' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n\r\n// Optional: GET endpoint for statistics (admin use)\r\nexport async function GET() {\r\n  try {\r\n    await ensureDbInitialized();\r\n    \r\n    // You can add statistics here if needed\r\n    return NextResponse.json({\r\n      status: 'API is working',\r\n      timestamp: new Date().toISOString(),\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('Error in tickets API:', error);\r\n    return NextResponse.json(\r\n      { error: 'API error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AACxC;AACA;;;;;;;AAGA,uCAAuC;AACvC,IAAI,gBAAgB;AAEpB,eAAe;IACb,IAAI,CAAC,eAAe;QAClB,IAAI;YACF,MAAM,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD;YACvB,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM;QAEN,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,GAAuB;QAEvC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,MAAM,UAAU,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAEjF,mBAAmB;QACnB,MAAM,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD,EAAE,SAAS;QAE1B,uBAAuB;QACvB,MAAM,UAAU,QAAQ,GAAG,CAAC,QAAQ,IACrB,CAAC,QAAQ,OAAO,CAAC,GAAG,CAAC,UACpB,GAAG,QAAQ,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,GAAG,EAAE,QAAQ,OAAO,CAAC,GAAG,CAAC,SAAS,GACzF,uBAAuB;QAEvC,MAAM,WAAW,GAAG,QAAQ,OAAO,EAAE,SAAS;QAE9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT;YACA;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QAEN,wCAAwC;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,WAAW,IAAI,OAAO,WAAW;QACnC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAY,GACrB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}