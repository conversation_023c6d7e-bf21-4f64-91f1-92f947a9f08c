'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { formatTicket, type Ticket } from '@/lib/ticketGenerator';

interface TicketData {
  id: string;
  ticket: Ticket;
  createdAt: string;
  expiresAt: string;
}

export default function SharedTicketPage() {
  const params = useParams();
  const router = useRouter();
  const [ticketData, setTicketData] = useState<TicketData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadTicket = async () => {
      try {
        const ticketId = params.id as string;

        // First try to load from database via API
        try {
          const response = await fetch(`/api/tickets/${ticketId}`);

          if (response.ok) {
            const result = await response.json();
            if (result.success) {
              setTicketData(result.data);
              setLoading(false);
              return;
            }
          }
        } catch (apiError) {
          console.warn('API failed, trying localStorage:', apiError);
        }

        // Fallback to localStorage
        const localData = localStorage.getItem(`ticket_${ticketId}`);
        if (localData) {
          const parsedData = JSON.parse(localData);

          // Check if ticket has expired
          const expiresAt = new Date(parsedData.expiresAt);
          if (expiresAt > new Date()) {
            setTicketData({
              id: ticketId,
              ticket: parsedData.ticket,
              createdAt: parsedData.createdAt,
              expiresAt: parsedData.expiresAt
            });
            setLoading(false);
            return;
          } else {
            // Remove expired ticket
            localStorage.removeItem(`ticket_${ticketId}`);
          }
        }

        // If we get here, ticket not found
        setError('Biletul nu a fost găsit sau a expirat.');
        setLoading(false);
      } catch (err) {
        console.error('Error loading ticket:', err);
        setError('Eroare la încărcarea biletului.');
        setLoading(false);
      }
    };

    loadTicket();
  }, [params.id]);

  const copyCurrentUrl = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      alert('Link copiat în clipboard!');
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const goToHome = () => {
    router.push('/');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-darker-surface to-dark-surface flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-300">Se încarcă biletul...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-darker-surface to-dark-surface">
        <header className="border-b border-dark-surface bg-darker-surface/80 backdrop-blur-sm">
          <div className="container mx-auto px-4 py-4 sm:py-6">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl sm:text-3xl font-bold gradient-text">AIpariat</h1>
              <button
                onClick={goToHome}
                className="px-3 sm:px-4 py-2 bg-primary text-black rounded-lg hover:bg-primary-dark transition-colors text-sm sm:text-base"
              >
                Acasă
              </button>
            </div>
          </div>
        </header>

        <div className="container mx-auto px-4 py-16 text-center">
          <div className="max-w-md mx-auto">
            <div className="text-6xl mb-6">😞</div>
            <h2 className="text-2xl font-bold text-white mb-4">Bilet indisponibil</h2>
            <p className="text-gray-300 mb-8">{error}</p>
            <button
              onClick={goToHome}
              className="px-6 py-3 bg-primary text-black font-semibold rounded-lg hover:bg-primary-dark transition-colors"
            >
              Creează un bilet nou
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!ticketData) {
    return null;
  }

  const timeLeft = new Date(ticketData.expiresAt).getTime() - new Date().getTime();
  const daysLeft = Math.ceil(timeLeft / (1000 * 60 * 60 * 24));

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-darker-surface to-dark-surface">
      <header className="border-b border-dark-surface bg-darker-surface/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 sm:py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl sm:text-3xl font-bold gradient-text">AIpariat</h1>
            <button
              onClick={goToHome}
              className="px-3 sm:px-4 py-2 bg-primary text-black rounded-lg hover:bg-primary-dark transition-colors text-sm sm:text-base"
            >
              <span className="hidden sm:inline">Creează biletul tău</span>
              <span className="sm:hidden">Creează bilet</span>
            </button>
          </div>
          <p className="text-gray-300 mt-2 text-sm sm:text-base">Bilet partajat</p>
        </div>
      </header>

      <div className="container mx-auto px-4 py-4 sm:py-8 max-w-4xl">
        <div className="bg-dark-surface rounded-lg border border-primary/20 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 gap-2">
            <h2 className="text-lg sm:text-xl font-semibold text-primary">Bilet Partajat</h2>
            <div className="text-xs sm:text-sm text-gray-400">
              Expiră în {daysLeft} {daysLeft === 1 ? 'zi' : 'zile'}
            </div>
          </div>

          <div className="bg-darker-surface rounded-lg p-4 sm:p-6 mb-4 sm:mb-6">
            <div className="whitespace-pre-line text-white text-sm sm:text-base">
              {formatTicket(ticketData.ticket)}
            </div>
          </div>

          <div className="flex flex-col gap-3 sm:gap-4">
            <button
              onClick={copyCurrentUrl}
              className="w-full px-4 sm:px-6 py-3 bg-primary/20 border border-primary/30 text-white rounded-lg hover:bg-primary/30 transition-colors flex items-center justify-center space-x-2 text-sm sm:text-base"
            >
              <span>🔗</span>
              <span>Copiază link-ul</span>
            </button>

            <button
              onClick={goToHome}
              className="w-full px-4 sm:px-6 py-3 bg-primary text-black font-semibold rounded-lg hover:bg-primary-dark transition-colors text-sm sm:text-base"
            >
              <span className="hidden sm:inline">Creează biletul tău</span>
              <span className="sm:hidden">Creează bilet nou</span>
            </button>
          </div>

          <div className="mt-6 pt-6 border-t border-primary/20 text-center text-sm text-gray-400">
            <p>Creat pe {new Date(ticketData.createdAt).toLocaleDateString('ro-RO', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}</p>
          </div>
        </div>

        <footer className="mt-16 text-center text-gray-400 text-sm">
          <div className="border-t border-dark-surface pt-8">
            <p>&copy; 2025 AIpariat. Toate drepturile rezervate.</p>
            <p className="mt-2">
              Această platformă are exclusiv rol consultativ și nu poate fi folosită pentru plasarea pariurilor sportive.
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
}
