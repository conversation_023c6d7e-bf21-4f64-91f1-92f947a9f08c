'use client';

import { useState, useEffect, useRef } from 'react';
import { generatePredefinedTicket, generateChatTicket, formatTicket, type Ticket } from '@/lib/ticketGenerator';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  ticketId?: string;
  shareUrl?: string;
}

export default function Home() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingTime, setLoadingTime] = useState(0);
  const [loadingMessage, setLoadingMessage] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Timer helper functions
  const startTimer = (initialMessage: string) => {
    setLoadingTime(0);
    setLoadingMessage(initialMessage);

    timerRef.current = setInterval(() => {
      setLoadingTime(prev => {
        const newTime = prev + 1;

        // Update loading message based on time elapsed
        if (newTime <= 5) {
          setLoadingMessage('🤖 AI-ul analizează cererea ta...');
        } else if (newTime <= 10) {
          setLoadingMessage('📊 Colectez date despre meciuri...');
        } else if (newTime <= 15) {
          setLoadingMessage('⚽ Analizez statistici și cote...');
        } else if (newTime <= 20) {
          setLoadingMessage('🎯 Calculez predicții optimale...');
        } else if (newTime <= 25) {
          setLoadingMessage('💎 Finalizez biletul tău...');
        } else {
          setLoadingMessage('⏳ Încă lucrez la biletul perfect...');
        }

        return newTime;
      });
    }, 1000);
  };

  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    setLoadingTime(0);
    setLoadingMessage('');
  };

  // Auto-scroll to bottom when messages change
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isLoading]);

  // Mobile fullscreen helpers
  const enterFullscreen = () => {
    if (window.innerWidth <= 768) { // Only on mobile
      setIsFullscreen(true);
      document.body.style.overflow = 'hidden';
    }
  };

  const exitFullscreen = () => {
    setIsFullscreen(false);
    document.body.style.overflow = 'auto';
  };

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      // Cleanup fullscreen on unmount
      document.body.style.overflow = 'auto';
    };
  }, []);

  // Generate shareable URL for ticket
  const generateShareUrl = async (ticket: Ticket): Promise<string> => {
    try {
      const response = await fetch('/api/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ticket }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create shared ticket: ${response.status}`);
      }

      const data = await response.json();

      // Store ticket in localStorage as backup
      const shareId = data.shareId;
      if (shareId) {
        localStorage.setItem(`ticket_${shareId}`, JSON.stringify({
          ticket,
          createdAt: new Date().toISOString(),
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
        }));
      }

      return data.shareUrl;
    } catch (error) {
      console.error('Error creating share URL:', error);

      // Fallback: create local-only shareable ticket
      const ticketId = `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem(`ticket_${ticketId}`, JSON.stringify({
        ticket,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        localOnly: true
      }));

      return `${window.location.origin}/bilet/${ticketId}`;
    }
  };

  // Copy share URL to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert('Link copiat în clipboard!');
    } catch (err) {
      console.error('Failed to copy: ', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Link copiat în clipboard!');
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    // Enter fullscreen on mobile
    enterFullscreen();

    const userMessage = {
      id: Date.now().toString(),
      text: inputValue,
      isUser: true
    };

    setMessages(prev => [...prev, userMessage]);
    const queryText = inputValue;
    setInputValue('');
    setIsLoading(true);
    startTimer('🤖 AI-ul analizează cererea ta...');

    try {
      // Generate ticket using real API
      const ticket = await generateChatTicket(queryText);
      const shareUrl = await generateShareUrl(ticket);
      const aiResponse = {
        id: (Date.now() + 1).toString(),
        text: formatTicket(ticket),
        isUser: false,
        ticketId: ticket.id,
        shareUrl: shareUrl
      };
      setMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      console.error('Error generating ticket:', error);
      const errorResponse = {
        id: (Date.now() + 1).toString(),
        text: "❌ Ne pare rău, a apărut o problemă la generarea biletului. Te rugăm să încerci din nou.",
        isUser: false
      };
      setMessages(prev => [...prev, errorResponse]);
    } finally {
      stopTimer();
      setIsLoading(false);
    }
  };

  const handlePredefinedButton = async (type: string) => {
    // Enter fullscreen on mobile
    enterFullscreen();

    setIsLoading(true);

    // Set specific timer message based on button type
    const getTimerMessage = (buttonType: string) => {
      switch (buttonType) {
        case 'curajos':
          return '🔥 Caut cele mai curajoase pariuri...';
        case 'sigur':
          return '🛡️ Analizez pariurile sigure...';
        case 'ziua':
          return '⭐ Pregătesc recomandarea zilei...';
        case 'saptamana':
          return '👑 Creez biletul premium al săptămânii...';
        default:
          return '🤖 AI-ul lucrează la biletul tău...';
      }
    };

    startTimer(getTimerMessage(type));

    try {
      // Generate ticket using real API
      const ticket = await generatePredefinedTicket(type);
      const shareUrl = await generateShareUrl(ticket);
      const response = {
        id: Date.now().toString(),
        text: formatTicket(ticket),
        isUser: false,
        ticketId: ticket.id,
        shareUrl: shareUrl
      };
      setMessages(prev => [...prev, response]);
    } catch (error) {
      console.error('Error generating predefined ticket:', error);
      const errorResponse = {
        id: Date.now().toString(),
        text: "❌ Ne pare rău, a apărut o problemă la generarea biletului. Te rugăm să încerci din nou.",
        isUser: false
      };
      setMessages(prev => [...prev, errorResponse]);
    } finally {
      stopTimer();
      setIsLoading(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-darker-surface to-dark-surface">
      {/* Header */}
      <header className="border-b border-dark-surface bg-darker-surface/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 sm:py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl sm:text-3xl font-bold gradient-text">
              AIpariat
            </h1>
            <div className="flex items-center space-x-2 text-xs sm:text-sm text-gray-400">
              <span className="w-2 h-2 bg-primary rounded-full animate-pulse"></span>
              <span className="hidden sm:inline">AI Activ</span>
              <span className="sm:hidden">AI</span>
            </div>
          </div>
          <p className="text-gray-300 mt-2 text-sm sm:text-base">
            Toate casele de pariuri folosesc algoritmi pentru garantarea profitului. Am creat AIpariat pentru egalizarea șanselor.
          </p>
        </div>
      </header>

      <div className="container mx-auto px-4 py-4 sm:py-8 max-w-6xl">
        <div className="grid lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Predefined Buttons */}
          <div className="lg:col-span-1 order-2 lg:order-1">
            <h2 className="text-lg sm:text-xl font-semibold mb-4 sm:mb-6 text-primary">
              Bilete Rapide
            </h2>
            <div className="space-y-3 sm:space-y-4">
              <button
                onClick={() => handlePredefinedButton('curajos')}
                disabled={isLoading}
                className="w-full p-3 sm:p-4 bg-gradient-to-r from-casino-red to-secondary rounded-lg border border-casino-red/30 hover:border-casino-red/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95"
              >
                <div className="text-left">
                  <div className="font-semibold text-white text-sm sm:text-base">🔥 Cel mai curajos bilet</div>
                  <div className="text-xs sm:text-sm text-gray-200 mt-1">Cote mari, risc ridicat</div>
                </div>
              </button>

              <button
                onClick={() => handlePredefinedButton('sigur')}
                disabled={isLoading}
                className="w-full p-3 sm:p-4 bg-gradient-to-r from-casino-green to-primary rounded-lg border border-casino-green/30 hover:border-casino-green/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95"
              >
                <div className="text-left">
                  <div className="font-semibold text-white text-sm sm:text-base">🛡️ Cel mai sigur bilet</div>
                  <div className="text-xs sm:text-sm text-gray-200 mt-1">Cote mici, șanse mari</div>
                </div>
              </button>

              <button
                onClick={() => handlePredefinedButton('ziua')}
                disabled={isLoading}
                className="w-full p-3 sm:p-4 bg-gradient-to-r from-ai-blue to-ai-purple rounded-lg border border-ai-blue/30 hover:border-ai-blue/60 transition-all duration-300 glow-ai hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95"
              >
                <div className="text-left">
                  <div className="font-semibold text-white text-sm sm:text-base">⭐ Recomandarea zilei</div>
                  <div className="text-xs sm:text-sm text-gray-200 mt-1">Selecția AI pentru astăzi</div>
                </div>
              </button>

              <button
                onClick={() => handlePredefinedButton('saptamana')}
                disabled={isLoading}
                className="w-full p-3 sm:p-4 bg-gradient-to-r from-accent to-secondary rounded-lg border border-accent/30 hover:border-accent/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95"
              >
                <div className="text-left">
                  <div className="font-semibold text-black text-sm sm:text-base">👑 Recomandarea săptămânii</div>
                  <div className="text-xs sm:text-sm text-gray-800 mt-1">Biletul premium al săptămânii</div>
                </div>
              </button>
            </div>
          </div>

          {/* Chat Interface */}
          <div
            ref={chatContainerRef}
            className={`lg:col-span-2 order-1 lg:order-2 ${
              isFullscreen
                ? 'fixed inset-0 z-50 bg-dark-background p-4 flex flex-col'
                : ''
            }`}
          >
            <div className={`flex items-center justify-between mb-4 sm:mb-6 ${isFullscreen ? 'flex-shrink-0' : ''}`}>
              <h2 className="text-lg sm:text-xl font-semibold text-primary">
                Chat cu AI-ul nostru
              </h2>
              {isFullscreen && (
                <button
                  onClick={exitFullscreen}
                  className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-800 transition-colors"
                  aria-label="Ieși din fullscreen"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>

            {/* Chat Messages */}
            <div className={`bg-dark-surface rounded-lg border border-primary/20 overflow-y-auto p-3 sm:p-4 mb-4 ${
              isFullscreen
                ? 'flex-1 h-full'
                : 'h-64 sm:h-80 lg:h-96'
            }`}>
              {messages.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-400">
                  <div className="text-center">
                    <div className="text-4xl mb-4">🤖</div>
                    <p>Salut! Sunt AIpariat, expertul în pariuri sportive care folosește cele mai avansate analize AI.</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
                    >
                      <div
                        className={`max-w-[280px] sm:max-w-xs lg:max-w-md px-3 sm:px-4 py-2 rounded-lg ${
                          message.isUser
                            ? 'bg-primary text-black'
                            : 'bg-darker-surface text-white border border-primary/30'
                        }`}
                      >
                        <div className="whitespace-pre-line text-sm">
                          {message.text}
                        </div>
                        {!message.isUser && message.shareUrl && (
                          <div className="mt-3 pt-3 border-t border-primary/20">
                            <button
                              onClick={() => copyToClipboard(message.shareUrl!)}
                              className="flex items-center space-x-2 text-xs bg-primary/20 hover:bg-primary/30 px-3 py-1 rounded transition-colors"
                            >
                              <span>🔗</span>
                              <span>Partajează biletul</span>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="bg-darker-surface text-white border border-primary/30 px-4 py-3 rounded-lg max-w-[320px] sm:max-w-sm">
                        {/* Timer and Progress Bar */}
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
                            <span className="text-xs font-medium text-primary">
                              {Math.floor(loadingTime / 60)}:{(loadingTime % 60).toString().padStart(2, '0')}
                            </span>
                          </div>
                          <div className="text-xs text-gray-400">
                            AI lucrează...
                          </div>
                        </div>

                        {/* Progress Bar */}
                        <div className="w-full bg-gray-700 rounded-full h-1.5 mb-3">
                          <div
                            className="bg-gradient-to-r from-primary to-secondary h-1.5 rounded-full transition-all duration-1000 ease-out"
                            style={{
                              width: `${Math.min((loadingTime / 30) * 100, 95)}%`
                            }}
                          ></div>
                        </div>

                        {/* Loading Message */}
                        <div className="text-sm">
                          {loadingMessage}
                        </div>

                        {/* Animated Dots */}
                        <div className="flex items-center space-x-1 mt-2">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce"></div>
                          <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                          <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.3s'}}></div>
                        </div>

                        {/* Helpful tip after 15 seconds */}
                        {loadingTime > 15 && (
                          <div className="mt-3 pt-3 border-t border-primary/20 text-xs text-gray-400">
                            💡 AI-ul folosește date în timp real pentru cele mai precise predicții
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Chat Input */}
            <div className={`flex flex-col sm:flex-row gap-2 sm:gap-2 ${isFullscreen ? 'flex-shrink-0' : ''}`}>
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                onFocus={enterFullscreen}
                placeholder="Descrie biletul dorit"
                disabled={isLoading}
                className="flex-1 px-3 sm:px-4 py-3 bg-darker-surface border border-primary/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary disabled:opacity-50 text-sm sm:text-base"
              />
              <button
                onClick={handleSendMessage}
                disabled={isLoading || !inputValue.trim()}
                className="px-4 sm:px-6 py-3 bg-primary text-black font-semibold rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base whitespace-nowrap"
              >
                Trimite
              </button>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="mt-8 sm:mt-16 text-center text-gray-400 text-xs sm:text-sm">
          <div className="border-t border-dark-surface pt-6 sm:pt-8">
            <p>&copy; 2025 AIpariat. Toate drepturile rezervate.</p>
            <p className="mt-2">
              Această platformă are exclusiv rol consultativ și nu poate fi folosită pentru plasarea pariurilor sportive.
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
}
