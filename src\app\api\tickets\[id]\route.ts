// API route for getting shared tickets by ID
import { NextRequest, NextResponse } from 'next/server';
import { getTicket, initializeDatabase } from '@/lib/database';

// Initialize database on first request
let dbInitialized = false;

async function ensureDbInitialized() {
  if (!dbInitialized) {
    try {
      await initializeDatabase();
      dbInitialized = true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const ticketId = resolvedParams.id;

    if (!ticketId) {
      return NextResponse.json(
        { error: 'Ticket ID is required' },
        { status: 400 }
      );
    }

    // Try to get ticket from database
    try {
      await ensureDbInitialized();
      const ticketData = await getTicket(ticketId);

      if (ticketData) {
        return NextResponse.json({
          success: true,
          data: ticketData,
        });
      }
    } catch (dbError) {
      console.warn('Database access failed for ticket retrieval:', dbError);
      // Continue to return 404 - client will try localStorage
    }

    // If database fails or ticket not found, return 404
    // Client will try localStorage as fallback
    return NextResponse.json(
      { error: 'Ticket not found or expired' },
      { status: 404 }
    );

  } catch (error) {
    console.error('Error getting shared ticket:', error);
    return NextResponse.json(
      { error: 'Failed to get shared ticket' },
      { status: 500 }
    );
  }
}
