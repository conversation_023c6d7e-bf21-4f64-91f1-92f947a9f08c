{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/src/lib/ticketGenerator.ts"], "sourcesContent": ["// Ticket Generator for AIpariat\r\n// Generates realistic football betting tickets with Romanian teams and international matches\r\n\r\nexport interface Match {\r\n  team1: string;\r\n  team2: string;\r\n  prediction: string;\r\n  odds: string;\r\n  league: string;\r\n  time?: string;\r\n}\r\n\r\nexport interface Ticket {\r\n  id: string;\r\n  type: string;\r\n  matches: Match[];\r\n  totalOdds: number;\r\n  description: string;\r\n  confidence: number;\r\n}\r\n\r\n// Romanian teams and leagues\r\nconst romanianTeams = [\r\n  \"FCSB\", \"CFR Cluj\", \"Universitatea Craiova\", \"Rapid București\", \r\n  \"Sepsi OSK\", \"UTA Arad\", \"Farul Constanța\", \"Voluntari\",\r\n  \"Dinamo București\", \"Petrolul Ploiești\", \"Hermannstadt\", \"Botoșani\"\r\n];\r\n\r\n// International teams\r\nconst internationalTeams = [\r\n  \"Real Madrid\", \"Barcelona\", \"Manchester City\", \"Liverpool\", \r\n  \"Bayern Munich\", \"PSG\", \"Juventus\", \"Inter Milan\", \"AC Milan\",\r\n  \"Arsenal\", \"Chelsea\", \"Tottenham\", \"Manchester United\",\r\n  \"Atletico Madrid\", \"Borussia Dortmund\", \"RB Leipzig\",\r\n  \"Napoli\", \"Roma\", \"Lazio\", \"Atalanta\", \"Brighton\", \"Newcastle\"\r\n];\r\n\r\nconst leagues = [\r\n  \"Liga 1 România\", \"Premier League\", \"La Liga\", \"Serie A\", \r\n  \"Bundesliga\", \"Ligue 1\", \"Champions League\", \"Europa League\"\r\n];\r\n\r\n// Prediction types with Romanian explanations\r\nconst predictionTypes = {\r\n  \"1\": \"Victorie gazdă\",\r\n  \"X\": \"Egalitate\", \r\n  \"2\": \"Victorie oaspete\",\r\n  \"1X\": \"Gazdă sau egal\",\r\n  \"X2\": \"Oaspete sau egal\",\r\n  \"12\": \"Victorie (fără egal)\",\r\n  \"Over 2.5\": \"Peste 2.5 goluri\",\r\n  \"Under 2.5\": \"Sub 2.5 goluri\",\r\n  \"BTTS\": \"Ambele echipe marchează\"\r\n};\r\n\r\n// Generate random odds based on prediction type\r\nfunction generateOdds(prediction: string): string {\r\n  const oddsRanges: { [key: string]: [number, number] } = {\r\n    \"1\": [1.20, 3.50],\r\n    \"X\": [2.80, 4.20],\r\n    \"2\": [1.15, 4.00],\r\n    \"1X\": [1.10, 1.80],\r\n    \"X2\": [1.05, 2.20],\r\n    \"12\": [1.05, 1.40],\r\n    \"Over 2.5\": [1.40, 2.20],\r\n    \"Under 2.5\": [1.50, 2.80],\r\n    \"BTTS\": [1.60, 2.40]\r\n  };\r\n\r\n  const [min, max] = oddsRanges[prediction] || [1.50, 3.00];\r\n  const odds = Math.random() * (max - min) + min;\r\n  return odds.toFixed(2);\r\n}\r\n\r\n// Generate a random match\r\nfunction generateMatch(useRomanianTeams: boolean = false): Match {\r\n  const teams = useRomanianTeams ? romanianTeams : internationalTeams;\r\n  const league = useRomanianTeams ? \"Liga 1 România\" : leagues[Math.floor(Math.random() * (leagues.length - 1)) + 1];\r\n  \r\n  const team1 = teams[Math.floor(Math.random() * teams.length)];\r\n  let team2 = teams[Math.floor(Math.random() * teams.length)];\r\n  while (team2 === team1) {\r\n    team2 = teams[Math.floor(Math.random() * teams.length)];\r\n  }\r\n\r\n  const predictions = Object.keys(predictionTypes);\r\n  const prediction = predictions[Math.floor(Math.random() * predictions.length)];\r\n  const odds = generateOdds(prediction);\r\n\r\n  return {\r\n    team1,\r\n    team2,\r\n    prediction,\r\n    odds,\r\n    league,\r\n    time: `${Math.floor(Math.random() * 24)}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`\r\n  };\r\n}\r\n\r\n// Generate predefined tickets\r\nexport function generatePredefinedTicket(type: string): Ticket {\r\n  let matches: Match[] = [];\r\n  let description = \"\";\r\n  let confidence = 0;\r\n\r\n  switch (type) {\r\n    case \"curajos\":\r\n      // High odds, risky bets\r\n      matches = [\r\n        generateMatch(),\r\n        generateMatch(),\r\n        generateMatch(true) // Include Romanian match\r\n      ];\r\n      // Adjust odds to be higher\r\n      matches = matches.map(match => ({\r\n        ...match,\r\n        odds: (parseFloat(match.odds) * 1.5 + 1).toFixed(2)\r\n      }));\r\n      description = \"🔥 **Cel mai curajos bilet** - Cote mari pentru câștiguri spectaculoase!\";\r\n      confidence = 25;\r\n      break;\r\n\r\n    case \"sigur\":\r\n      // Low odds, safe bets\r\n      matches = [\r\n        generateMatch(),\r\n        generateMatch(),\r\n        generateMatch()\r\n      ];\r\n      // Adjust odds to be lower (safer)\r\n      matches = matches.map(match => ({\r\n        ...match,\r\n        prediction: Math.random() > 0.7 ? \"1\" : match.prediction, // More home wins\r\n        odds: Math.max(1.10, parseFloat(match.odds) * 0.6).toFixed(2)\r\n      }));\r\n      description = \"🛡️ **Cel mai sigur bilet** - Selecții cu șanse mari de câștig!\";\r\n      confidence = 85;\r\n      break;\r\n\r\n    case \"ziua\":\r\n      // Balanced daily recommendation\r\n      matches = [\r\n        generateMatch(),\r\n        generateMatch(true), // Romanian match\r\n        generateMatch()\r\n      ];\r\n      description = \"⭐ **Recomandarea zilei** - Analiza AI pentru cele mai bune oportunități!\";\r\n      confidence = 65;\r\n      break;\r\n\r\n    case \"saptamana\":\r\n      // Premium weekly recommendation\r\n      matches = [\r\n        generateMatch(),\r\n        generateMatch(),\r\n        generateMatch(),\r\n        generateMatch(true) // Include Romanian match\r\n      ];\r\n      description = \"👑 **Recomandarea săptămânii** - Biletul premium analizat în detaliu!\";\r\n      confidence = 75;\r\n      break;\r\n\r\n    default:\r\n      matches = [generateMatch(), generateMatch(), generateMatch()];\r\n      description = \"🎯 Bilet generat de AI\";\r\n      confidence = 60;\r\n  }\r\n\r\n  const totalOdds = matches.reduce((acc, match) => acc * parseFloat(match.odds), 1);\r\n\r\n  return {\r\n    id: Date.now().toString(),\r\n    type,\r\n    matches,\r\n    totalOdds: parseFloat(totalOdds.toFixed(2)),\r\n    description,\r\n    confidence\r\n  };\r\n}\r\n\r\n// Generate ticket based on user query\r\nexport function generateChatTicket(query: string): Ticket {\r\n  const lowerQuery = query.toLowerCase();\r\n  let matches: Match[] = [];\r\n  let description = \"\";\r\n  const confidence = 60;\r\n\r\n  // Analyze query for specific requests\r\n  if (lowerQuery.includes(\"sigur\") || lowerQuery.includes(\"safe\")) {\r\n    return generatePredefinedTicket(\"sigur\");\r\n  } else if (lowerQuery.includes(\"curajos\") || lowerQuery.includes(\"risc\")) {\r\n    return generatePredefinedTicket(\"curajos\");\r\n  } else if (lowerQuery.includes(\"român\") || lowerQuery.includes(\"liga 1\")) {\r\n    // Romanian league focus\r\n    matches = [\r\n      generateMatch(true),\r\n      generateMatch(true),\r\n      generateMatch(true)\r\n    ];\r\n    description = \"🇷🇴 **Bilet Liga 1 România** - Focalizat pe campionatul nostru!\";\r\n  } else if (lowerQuery.includes(\"champions\") || lowerQuery.includes(\"europa\")) {\r\n    // European competitions\r\n    matches = [\r\n      { ...generateMatch(), league: \"Champions League\" },\r\n      { ...generateMatch(), league: \"Champions League\" },\r\n      { ...generateMatch(), league: \"Europa League\" }\r\n    ];\r\n    description = \"🏆 **Bilet Competiții Europene** - Meciurile mari ale săptămânii!\";\r\n  } else {\r\n    // General ticket\r\n    matches = [\r\n      generateMatch(),\r\n      generateMatch(Math.random() > 0.5), // 50% chance for Romanian match\r\n      generateMatch()\r\n    ];\r\n    description = \"🎯 **Bilet personalizat** - Generat special pentru tine!\";\r\n  }\r\n\r\n  const totalOdds = matches.reduce((acc, match) => acc * parseFloat(match.odds), 1);\r\n\r\n  return {\r\n    id: Date.now().toString(),\r\n    type: \"chat\",\r\n    matches,\r\n    totalOdds: parseFloat(totalOdds.toFixed(2)),\r\n    description,\r\n    confidence\r\n  };\r\n}\r\n\r\n// Format ticket for display\r\nexport function formatTicket(ticket: Ticket): string {\r\n  let formatted = `${ticket.description}\\n\\n`;\r\n  \r\n  ticket.matches.forEach((match, index) => {\r\n    const predictionText = predictionTypes[match.prediction as keyof typeof predictionTypes] || match.prediction;\r\n    formatted += `${index + 1}. **${match.team1}** vs **${match.team2}**\\n`;\r\n    formatted += `   📍 ${match.league}`;\r\n    if (match.time) formatted += ` | ⏰ ${match.time}`;\r\n    formatted += `\\n   🎯 ${predictionText} | 💰 Cotă: ${match.odds}\\n\\n`;\r\n  });\r\n  \r\n  formatted += `💎 **Cotă totală: ${ticket.totalOdds}**\\n`;\r\n  formatted += `📊 **Încredere AI: ${ticket.confidence}%**`;\r\n  \r\n  return formatted;\r\n}\r\n"], "names": [], "mappings": "AAAA,gCAAgC;AAChC,6FAA6F;;;;;;AAoB7F,6BAA6B;AAC7B,MAAM,gBAAgB;IACpB;IAAQ;IAAY;IAAyB;IAC7C;IAAa;IAAY;IAAmB;IAC5C;IAAoB;IAAqB;IAAgB;CAC1D;AAED,sBAAsB;AACtB,MAAM,qBAAqB;IACzB;IAAe;IAAa;IAAmB;IAC/C;IAAiB;IAAO;IAAY;IAAe;IACnD;IAAW;IAAW;IAAa;IACnC;IAAmB;IAAqB;IACxC;IAAU;IAAQ;IAAS;IAAY;IAAY;CACpD;AAED,MAAM,UAAU;IACd;IAAkB;IAAkB;IAAW;IAC/C;IAAc;IAAW;IAAoB;CAC9C;AAED,8CAA8C;AAC9C,MAAM,kBAAkB;IACtB,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,YAAY;IACZ,aAAa;IACb,QAAQ;AACV;AAEA,gDAAgD;AAChD,SAAS,aAAa,UAAkB;IACtC,MAAM,aAAkD;QACtD,KAAK;YAAC;YAAM;SAAK;QACjB,KAAK;YAAC;YAAM;SAAK;QACjB,KAAK;YAAC;YAAM;SAAK;QACjB,MAAM;YAAC;YAAM;SAAK;QAClB,MAAM;YAAC;YAAM;SAAK;QAClB,MAAM;YAAC;YAAM;SAAK;QAClB,YAAY;YAAC;YAAM;SAAK;QACxB,aAAa;YAAC;YAAM;SAAK;QACzB,QAAQ;YAAC;YAAM;SAAK;IACtB;IAEA,MAAM,CAAC,KAAK,IAAI,GAAG,UAAU,CAAC,WAAW,IAAI;QAAC;QAAM;KAAK;IACzD,MAAM,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI;IAC3C,OAAO,KAAK,OAAO,CAAC;AACtB;AAEA,0BAA0B;AAC1B,SAAS,cAAc,mBAA4B,KAAK;IACtD,MAAM,QAAQ,mBAAmB,gBAAgB;IACjD,MAAM,SAAS,mBAAmB,mBAAmB,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,QAAQ,MAAM,GAAG,CAAC,KAAK,EAAE;IAElH,MAAM,QAAQ,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;IAC7D,IAAI,QAAQ,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;IAC3D,MAAO,UAAU,MAAO;QACtB,QAAQ,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;IACzD;IAEA,MAAM,cAAc,OAAO,IAAI,CAAC;IAChC,MAAM,aAAa,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;IAC9E,MAAM,OAAO,aAAa;IAE1B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,MAAM,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACzG;AACF;AAGO,SAAS,yBAAyB,IAAY;IACnD,IAAI,UAAmB,EAAE;IACzB,IAAI,cAAc;IAClB,IAAI,aAAa;IAEjB,OAAQ;QACN,KAAK;YACH,wBAAwB;YACxB,UAAU;gBACR;gBACA;gBACA,cAAc,MAAM,yBAAyB;aAC9C;YACD,2BAA2B;YAC3B,UAAU,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC9B,GAAG,KAAK;oBACR,MAAM,CAAC,WAAW,MAAM,IAAI,IAAI,MAAM,CAAC,EAAE,OAAO,CAAC;gBACnD,CAAC;YACD,cAAc;YACd,aAAa;YACb;QAEF,KAAK;YACH,sBAAsB;YACtB,UAAU;gBACR;gBACA;gBACA;aACD;YACD,kCAAkC;YAClC,UAAU,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC9B,GAAG,KAAK;oBACR,YAAY,KAAK,MAAM,KAAK,MAAM,MAAM,MAAM,UAAU;oBACxD,MAAM,KAAK,GAAG,CAAC,MAAM,WAAW,MAAM,IAAI,IAAI,KAAK,OAAO,CAAC;gBAC7D,CAAC;YACD,cAAc;YACd,aAAa;YACb;QAEF,KAAK;YACH,gCAAgC;YAChC,UAAU;gBACR;gBACA,cAAc;gBACd;aACD;YACD,cAAc;YACd,aAAa;YACb;QAEF,KAAK;YACH,gCAAgC;YAChC,UAAU;gBACR;gBACA;gBACA;gBACA,cAAc,MAAM,yBAAyB;aAC9C;YACD,cAAc;YACd,aAAa;YACb;QAEF;YACE,UAAU;gBAAC;gBAAiB;gBAAiB;aAAgB;YAC7D,cAAc;YACd,aAAa;IACjB;IAEA,MAAM,YAAY,QAAQ,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,WAAW,MAAM,IAAI,GAAG;IAE/E,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB;QACA;QACA,WAAW,WAAW,UAAU,OAAO,CAAC;QACxC;QACA;IACF;AACF;AAGO,SAAS,mBAAmB,KAAa;IAC9C,MAAM,aAAa,MAAM,WAAW;IACpC,IAAI,UAAmB,EAAE;IACzB,IAAI,cAAc;IAClB,MAAM,aAAa;IAEnB,sCAAsC;IACtC,IAAI,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,SAAS;QAC/D,OAAO,yBAAyB;IAClC,OAAO,IAAI,WAAW,QAAQ,CAAC,cAAc,WAAW,QAAQ,CAAC,SAAS;QACxE,OAAO,yBAAyB;IAClC,OAAO,IAAI,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,WAAW;QACxE,wBAAwB;QACxB,UAAU;YACR,cAAc;YACd,cAAc;YACd,cAAc;SACf;QACD,cAAc;IAChB,OAAO,IAAI,WAAW,QAAQ,CAAC,gBAAgB,WAAW,QAAQ,CAAC,WAAW;QAC5E,wBAAwB;QACxB,UAAU;YACR;gBAAE,GAAG,eAAe;gBAAE,QAAQ;YAAmB;YACjD;gBAAE,GAAG,eAAe;gBAAE,QAAQ;YAAmB;YACjD;gBAAE,GAAG,eAAe;gBAAE,QAAQ;YAAgB;SAC/C;QACD,cAAc;IAChB,OAAO;QACL,iBAAiB;QACjB,UAAU;YACR;YACA,cAAc,KAAK,MAAM,KAAK;YAC9B;SACD;QACD,cAAc;IAChB;IAEA,MAAM,YAAY,QAAQ,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,WAAW,MAAM,IAAI,GAAG;IAE/E,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,MAAM;QACN;QACA,WAAW,WAAW,UAAU,OAAO,CAAC;QACxC;QACA;IACF;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI,YAAY,GAAG,OAAO,WAAW,CAAC,IAAI,CAAC;IAE3C,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;QAC7B,MAAM,iBAAiB,eAAe,CAAC,MAAM,UAAU,CAAiC,IAAI,MAAM,UAAU;QAC5G,aAAa,GAAG,QAAQ,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC;QACvE,aAAa,CAAC,MAAM,EAAE,MAAM,MAAM,EAAE;QACpC,IAAI,MAAM,IAAI,EAAE,aAAa,CAAC,KAAK,EAAE,MAAM,IAAI,EAAE;QACjD,aAAa,CAAC,QAAQ,EAAE,eAAe,YAAY,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC;IACvE;IAEA,aAAa,CAAC,kBAAkB,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC;IACxD,aAAa,CAAC,mBAAmB,EAAE,OAAO,UAAU,CAAC,GAAG,CAAC;IAEzD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { generatePredefinedTicket, generateChatTicket, formatTicket, type Ticket } from '@/lib/ticketGenerator';\r\n\r\ninterface Message {\r\n  id: string;\r\n  text: string;\r\n  isUser: boolean;\r\n  ticketId?: string;\r\n  shareUrl?: string;\r\n}\r\n\r\nexport default function Home() {\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [inputValue, setInputValue] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  // Generate shareable URL for ticket\r\n  const generateShareUrl = async (ticket: Ticket): Promise<string> => {\r\n    try {\r\n      const response = await fetch('/api/tickets', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ ticket }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to create shared ticket');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return data.shareUrl;\r\n    } catch (error) {\r\n      console.error('Error creating share URL:', error);\r\n      // Fallback to a simple URL if API fails\r\n      const ticketId = `ticket_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n      return `${window.location.origin}/bilet/${ticketId}`;\r\n    }\r\n  };\r\n\r\n  // Copy share URL to clipboard\r\n  const copyToClipboard = async (text: string) => {\r\n    try {\r\n      await navigator.clipboard.writeText(text);\r\n      alert('Link copiat în clipboard!');\r\n    } catch (err) {\r\n      console.error('Failed to copy: ', err);\r\n      // Fallback for older browsers\r\n      const textArea = document.createElement('textarea');\r\n      textArea.value = text;\r\n      document.body.appendChild(textArea);\r\n      textArea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textArea);\r\n      alert('Link copiat în clipboard!');\r\n    }\r\n  };\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!inputValue.trim()) return;\r\n\r\n    const userMessage = {\r\n      id: Date.now().toString(),\r\n      text: inputValue,\r\n      isUser: true\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInputValue('');\r\n    setIsLoading(true);\r\n\r\n    // Simulate AI response\r\n    setTimeout(async () => {\r\n      const ticket = generateChatTicket(inputValue);\r\n      const shareUrl = await generateShareUrl(ticket);\r\n      const aiResponse = {\r\n        id: (Date.now() + 1).toString(),\r\n        text: formatTicket(ticket),\r\n        isUser: false,\r\n        ticketId: ticket.id,\r\n        shareUrl: shareUrl\r\n      };\r\n      setMessages(prev => [...prev, aiResponse]);\r\n      setIsLoading(false);\r\n    }, 1500);\r\n  };\r\n\r\n  const handlePredefinedButton = (type: string) => {\r\n    setIsLoading(true);\r\n\r\n    setTimeout(async () => {\r\n      const ticket = generatePredefinedTicket(type);\r\n      const shareUrl = await generateShareUrl(ticket);\r\n      const response = {\r\n        id: Date.now().toString(),\r\n        text: formatTicket(ticket),\r\n        isUser: false,\r\n        ticketId: ticket.id,\r\n        shareUrl: shareUrl\r\n      };\r\n      setMessages(prev => [...prev, response]);\r\n      setIsLoading(false);\r\n    }, 1000);\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-darker-surface to-dark-surface\">\r\n      {/* Header */}\r\n      <header className=\"border-b border-dark-surface bg-darker-surface/80 backdrop-blur-sm\">\r\n        <div className=\"container mx-auto px-4 py-4 sm:py-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h1 className=\"text-2xl sm:text-3xl font-bold gradient-text\">\r\n              AIpariat\r\n            </h1>\r\n            <div className=\"flex items-center space-x-2 text-xs sm:text-sm text-gray-400\">\r\n              <span className=\"w-2 h-2 bg-primary rounded-full animate-pulse\"></span>\r\n              <span className=\"hidden sm:inline\">AI Activ</span>\r\n              <span className=\"sm:hidden\">AI</span>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-gray-300 mt-2 text-sm sm:text-base\">\r\n            Toate casele de pariuri folosesc algoritmi pentru garantarea profitului. Am creat AIpariat pentru egalizarea șanselor.\r\n          </p>\r\n        </div>\r\n      </header>\r\n\r\n      <div className=\"container mx-auto px-4 py-4 sm:py-8 max-w-6xl\">\r\n        <div className=\"grid lg:grid-cols-3 gap-6 lg:gap-8\">\r\n          {/* Predefined Buttons */}\r\n          <div className=\"lg:col-span-1 order-2 lg:order-1\">\r\n            <h2 className=\"text-lg sm:text-xl font-semibold mb-4 sm:mb-6 text-primary\">\r\n              Bilete Rapide\r\n            </h2>\r\n            <div className=\"space-y-3 sm:space-y-4\">\r\n              <button\r\n                onClick={() => handlePredefinedButton('curajos')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-casino-red to-secondary rounded-lg border border-casino-red/30 hover:border-casino-red/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">🔥 Cel mai curajos bilet</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Cote mari, risc ridicat</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('sigur')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-casino-green to-primary rounded-lg border border-casino-green/30 hover:border-casino-green/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">🛡️ Cel mai sigur bilet</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Cote mici, șanse mari</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('ziua')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-ai-blue to-ai-purple rounded-lg border border-ai-blue/30 hover:border-ai-blue/60 transition-all duration-300 glow-ai hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">⭐ Recomandarea zilei</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Selecția AI pentru astăzi</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('saptamana')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-accent to-secondary rounded-lg border border-accent/30 hover:border-accent/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-black text-sm sm:text-base\">👑 Recomandarea săptămânii</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-800 mt-1\">Biletul premium al săptămânii</div>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Chat Interface */}\r\n          <div className=\"lg:col-span-2 order-1 lg:order-2\">\r\n            <h2 className=\"text-lg sm:text-xl font-semibold mb-4 sm:mb-6 text-primary\">\r\n              Chat cu AI-ul nostru\r\n            </h2>\r\n\r\n            {/* Chat Messages */}\r\n            <div className=\"bg-dark-surface rounded-lg border border-primary/20 h-64 sm:h-80 lg:h-96 overflow-y-auto p-3 sm:p-4 mb-4\">\r\n              {messages.length === 0 ? (\r\n                <div className=\"flex items-center justify-center h-full text-gray-400\">\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-4xl mb-4\">🤖</div>\r\n                    <p>Salut! Sunt AIpariat, expertul în pariuri sportive care folosește cele mai avansate analize AI.</p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-4\">\r\n                  {messages.map((message) => (\r\n                    <div\r\n                      key={message.id}\r\n                      className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}\r\n                    >\r\n                      <div\r\n                        className={`max-w-[280px] sm:max-w-xs lg:max-w-md px-3 sm:px-4 py-2 rounded-lg ${\r\n                          message.isUser\r\n                            ? 'bg-primary text-black'\r\n                            : 'bg-darker-surface text-white border border-primary/30'\r\n                        }`}\r\n                      >\r\n                        <div className=\"whitespace-pre-line text-sm\">\r\n                          {message.text}\r\n                        </div>\r\n                        {!message.isUser && message.shareUrl && (\r\n                          <div className=\"mt-3 pt-3 border-t border-primary/20\">\r\n                            <button\r\n                              onClick={() => copyToClipboard(message.shareUrl!)}\r\n                              className=\"flex items-center space-x-2 text-xs bg-primary/20 hover:bg-primary/30 px-3 py-1 rounded transition-colors\"\r\n                            >\r\n                              <span>🔗</span>\r\n                              <span>Partajează biletul</span>\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                  {isLoading && (\r\n                    <div className=\"flex justify-start\">\r\n                      <div className=\"bg-darker-surface text-white border border-primary/30 px-4 py-2 rounded-lg\">\r\n                        <div className=\"flex items-center space-x-2\">\r\n                          <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce\"></div>\r\n                          <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\r\n                          <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Chat Input */}\r\n            <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-2\">\r\n              <input\r\n                type=\"text\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}\r\n                placeholder=\"Descrie biletul dorit\"\r\n                disabled={isLoading}\r\n                className=\"flex-1 px-3 sm:px-4 py-3 bg-darker-surface border border-primary/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary disabled:opacity-50 text-sm sm:text-base\"\r\n              />\r\n              <button\r\n                onClick={handleSendMessage}\r\n                disabled={isLoading || !inputValue.trim()}\r\n                className=\"px-4 sm:px-6 py-3 bg-primary text-black font-semibold rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base whitespace-nowrap\"\r\n              >\r\n                Trimite\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <footer className=\"mt-8 sm:mt-16 text-center text-gray-400 text-xs sm:text-sm\">\r\n          <div className=\"border-t border-dark-surface pt-6 sm:pt-8\">\r\n            <p>&copy; 2025 AIpariat. Toate drepturile rezervate.</p>\r\n            <p className=\"mt-2\">\r\n              Această platformă are exclusiv rol consultativ și nu poate fi folosită pentru plasarea pariurilor sportive.\r\n            </p>\r\n          </div>\r\n        </footer>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAae,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,oCAAoC;IACpC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,QAAQ;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wCAAwC;YACxC,MAAM,WAAW,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAClF,OAAO,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,UAAU;QACtD;IACF;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oBAAoB;YAClC,8BAA8B;YAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,MAAM;YACf,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,cAAc;YAClB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,QAAQ;QACV;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QACd,aAAa;QAEb,uBAAuB;QACvB,WAAW;YACT,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;YAClC,MAAM,WAAW,MAAM,iBAAiB;YACxC,MAAM,aAAa;gBACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,QAAQ;gBACR,UAAU,OAAO,EAAE;gBACnB,UAAU;YACZ;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YACzC,aAAa;QACf,GAAG;IACL;IAEA,MAAM,yBAAyB,CAAC;QAC9B,aAAa;QAEb,WAAW;YACT,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,2BAAwB,AAAD,EAAE;YACxC,MAAM,WAAW,MAAM,iBAAiB;YACxC,MAAM,WAAW;gBACf,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,QAAQ;gBACR,UAAU,OAAO,EAAE;gBACnB,UAAU;YACZ;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAS;YACvC,aAAa;QACf,GAAG;IACL;IAIA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAG7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,8OAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;;;;;;;sCAGhC,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAM3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAG3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,8OAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,8OAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,8OAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAK3E,8OAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;kEAAE;;;;;;;;;;;;;;;;iEAIP,8OAAC;4CAAI,WAAU;;gDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wDAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,gBAAgB,iBAAiB;kEAErE,cAAA,8OAAC;4DACC,WAAW,CAAC,mEAAmE,EAC7E,QAAQ,MAAM,GACV,0BACA,yDACJ;;8EAEF,8OAAC;oEAAI,WAAU;8EACZ,QAAQ,IAAI;;;;;;gEAEd,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,kBAClC,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,SAAS,IAAM,gBAAgB,QAAQ,QAAQ;wEAC/C,WAAU;;0FAEV,8OAAC;0FAAK;;;;;;0FACN,8OAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;uDApBT,QAAQ,EAAE;;;;;gDA2BlB,2BACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;oEAAiD,OAAO;wEAAC,gBAAgB;oEAAM;;;;;;8EAC9F,8OAAC;oEAAI,WAAU;oEAAiD,OAAO;wEAAC,gBAAgB;oEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAU5G,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;gDACxC,aAAY;gDACZ,UAAU;gDACV,WAAU;;;;;;0DAEZ,8OAAC;gDACC,SAAS;gDACT,UAAU,aAAa,CAAC,WAAW,IAAI;gDACvC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC", "debugId": null}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}