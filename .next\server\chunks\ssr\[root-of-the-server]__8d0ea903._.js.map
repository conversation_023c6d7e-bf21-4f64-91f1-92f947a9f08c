{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/src/lib/ticketGenerator.ts"], "sourcesContent": ["// Ticket Generator for AIpariat\r\n// Integrates with real AI API for betting slip generation\r\n\r\nexport interface Match {\r\n  match: string;\r\n  date: string;\r\n  sport: string;\r\n  bet_type: string;\r\n  selection: string;\r\n  odds: number;\r\n  league: string;\r\n}\r\n\r\nexport interface Ticket {\r\n  id: string;\r\n  type: string;\r\n  matches: Match[];\r\n  totalOdds: number;\r\n  description: string;\r\n  confidence: string;\r\n  explanation?: string;\r\n  recommended_stake?: number;\r\n  potential_win?: number;\r\n  risk_level?: string;\r\n}\r\n\r\n// API Configuration\r\nconst API_BASE_URL = 'https://aipariatagent-1914c9cd53a9.herokuapp.com';\r\n\r\n// API Response interfaces\r\ninterface ApiTaskResponse {\r\n  task_id: string;\r\n  status: string;\r\n  message: string;\r\n}\r\n\r\ninterface ApiStatusResponse {\r\n  task_id: string;\r\n  status: 'pending' | 'running' | 'completed' | 'failed';\r\n  progress?: number;\r\n  created_at?: string;\r\n  completed_at?: string;\r\n}\r\n\r\ninterface ApiBettingSlip {\r\n  matches: {\r\n    match: string;\r\n    date: string;\r\n    sport: string;\r\n    bet_type: string;\r\n    selection: string;\r\n    odds: number;\r\n    league: string;\r\n  }[];\r\n  total_odds: number;\r\n  recommended_stake?: number;\r\n  potential_win?: number;\r\n  risk_level?: string;\r\n  confidence: string;\r\n  explanation?: string;\r\n}\r\n\r\ninterface ApiResultResponse {\r\n  task_id: string;\r\n  status: string;\r\n  query: string;\r\n  betting_slip: ApiBettingSlip;\r\n  grounding_info?: {\r\n    has_grounding: boolean;\r\n    web_search_queries: string[];\r\n    sources: { title: string; uri: string }[];\r\n  };\r\n}\r\n\r\n// API Integration Functions\r\nasync function createBettingSlipTask(query: string, options: {\r\n  max_odds?: number;\r\n  budget?: number;\r\n  sport?: string;\r\n} = {}): Promise<string> {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/betting-slip/create`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        query,\r\n        ...options\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`API request failed: ${response.status}`);\r\n    }\r\n\r\n    const data: ApiTaskResponse = await response.json();\r\n    return data.task_id;\r\n  } catch (error) {\r\n    console.error('Error creating betting slip task:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nasync function pollTaskStatus(taskId: string): Promise<ApiResultResponse> {\r\n  const maxAttempts = 30; // 30 attempts * 2 seconds = 60 seconds max\r\n  let attempts = 0;\r\n\r\n  while (attempts < maxAttempts) {\r\n    try {\r\n      const response = await fetch(`${API_BASE_URL}/betting-slip/status/${taskId}`);\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Status check failed: ${response.status}`);\r\n      }\r\n\r\n      const statusData: ApiStatusResponse = await response.json();\r\n\r\n      if (statusData.status === 'completed') {\r\n        // Add a small delay to ensure result is ready\r\n        await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n        // Try to get the result with retry logic\r\n        let resultAttempts = 0;\r\n        const maxResultAttempts = 3;\r\n\r\n        while (resultAttempts < maxResultAttempts) {\r\n          try {\r\n            const resultResponse = await fetch(`${API_BASE_URL}/betting-slip/result/${taskId}`);\r\n\r\n            if (resultResponse.ok) {\r\n              return await resultResponse.json();\r\n            } else if (resultResponse.status === 404) {\r\n              console.warn(`Result not ready yet, attempt ${resultAttempts + 1}/${maxResultAttempts}`);\r\n              if (resultAttempts < maxResultAttempts - 1) {\r\n                await new Promise(resolve => setTimeout(resolve, 2000));\r\n                resultAttempts++;\r\n                continue;\r\n              }\r\n            }\r\n\r\n            throw new Error(`Result fetch failed: ${resultResponse.status}`);\r\n          } catch (resultError) {\r\n            if (resultAttempts >= maxResultAttempts - 1) {\r\n              throw resultError;\r\n            }\r\n            await new Promise(resolve => setTimeout(resolve, 2000));\r\n            resultAttempts++;\r\n          }\r\n        }\r\n      } else if (statusData.status === 'failed') {\r\n        throw new Error('Betting slip generation failed');\r\n      }\r\n\r\n      // Wait 2 seconds before next poll\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n      attempts++;\r\n    } catch (error) {\r\n      console.error('Error polling task status:', error);\r\n      if (attempts >= maxAttempts - 1) {\r\n        throw error;\r\n      }\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n      attempts++;\r\n    }\r\n  }\r\n\r\n  throw new Error('Task timeout - betting slip generation took too long');\r\n}\r\n\r\nfunction convertApiResponseToTicket(apiResponse: ApiResultResponse, type: string): Ticket {\r\n  const bettingSlip = apiResponse.betting_slip;\r\n\r\n  return {\r\n    id: Date.now().toString(),\r\n    type,\r\n    matches: bettingSlip.matches,\r\n    totalOdds: bettingSlip.total_odds,\r\n    description: getDescriptionForType(type),\r\n    confidence: bettingSlip.confidence,\r\n    explanation: bettingSlip.explanation,\r\n    recommended_stake: bettingSlip.recommended_stake,\r\n    potential_win: bettingSlip.potential_win,\r\n    risk_level: bettingSlip.risk_level\r\n  };\r\n}\r\n\r\nfunction getDescriptionForType(type: string): string {\r\n  switch (type) {\r\n    case \"curajos\":\r\n      return \"🔥 **Cel mai curajos bilet** - Cote mari pentru câștiguri spectaculoase!\";\r\n    case \"sigur\":\r\n      return \"🛡️ **Cel mai sigur bilet** - Selecții cu șanse mari de câștig!\";\r\n    case \"ziua\":\r\n      return \"⭐ **Recomandarea zilei** - Selecția AI pentru astăzi!\";\r\n    case \"saptamana\":\r\n      return \"👑 **Recomandarea săptămânii** - Biletul premium al săptămânii!\";\r\n    default:\r\n      return \"🎯 **Bilet personalizat** - Generat special pentru tine!\";\r\n  }\r\n}\r\n\r\nfunction getQueryForType(type: string): string {\r\n  switch (type) {\r\n    case \"curajos\":\r\n      return \"Vreau un bilet curajos cu cote mari și risc ridicat pentru meciuri din următoarele zile\";\r\n    case \"sigur\":\r\n      return \"Vreau un bilet sigur cu cote mici și șanse mari de câștig pentru meciuri din următoarele zile\";\r\n    case \"ziua\":\r\n      return \"Vreau recomandarea zilei pentru pariuri sportive cu analiză AI pentru astăzi\";\r\n    case \"saptamana\":\r\n      return \"Vreau recomandarea săptămânii pentru pariuri sportive, biletul premium al săptămânii\";\r\n    default:\r\n      return \"Vreau un bilet pentru pariuri sportive cu analiză AI\";\r\n  }\r\n}\r\n\r\n// Generate predefined tickets using real API\r\nexport async function generatePredefinedTicket(type: string): Promise<Ticket> {\r\n  try {\r\n    const query = getQueryForType(type);\r\n    const options: { max_odds?: number; budget?: number; sport?: string } = {};\r\n\r\n    // Set specific options based on type\r\n    switch (type) {\r\n      case \"curajos\":\r\n        options.max_odds = 10.0; // Higher odds for brave bets\r\n        break;\r\n      case \"sigur\":\r\n        options.max_odds = 3.0; // Lower odds for safe bets\r\n        break;\r\n      case \"ziua\":\r\n        options.sport = \"fotbal\";\r\n        break;\r\n      case \"saptamana\":\r\n        options.budget = 100.0;\r\n        break;\r\n    }\r\n\r\n    console.log(`🎯 Generating ${type} ticket with query: \"${query}\" and options:`, options);\r\n\r\n    const taskId = await createBettingSlipTask(query, options);\r\n    console.log(`✅ Task created with ID: ${taskId}`);\r\n\r\n    const apiResponse = await pollTaskStatus(taskId);\r\n    console.log(`✅ API response received for ${type} ticket`);\r\n\r\n    return convertApiResponseToTicket(apiResponse, type);\r\n  } catch (error) {\r\n    console.error(`❌ Error generating ${type} ticket:`, error);\r\n    // Return fallback ticket in case of API failure\r\n    return createFallbackTicket(type, undefined, error.message);\r\n  }\r\n}\r\n\r\n// Generate ticket based on user query using real API\r\nexport async function generateChatTicket(query: string): Promise<Ticket> {\r\n  try {\r\n    console.log(`💬 Generating chat ticket with query: \"${query}\"`);\r\n\r\n    const taskId = await createBettingSlipTask(query);\r\n    console.log(`✅ Chat task created with ID: ${taskId}`);\r\n\r\n    const apiResponse = await pollTaskStatus(taskId);\r\n    console.log(`✅ Chat API response received`);\r\n\r\n    return convertApiResponseToTicket(apiResponse, \"chat\");\r\n  } catch (error) {\r\n    console.error('❌ Error generating chat ticket:', error);\r\n    // Return fallback ticket in case of API failure\r\n    return createFallbackTicket(\"chat\", query, error.message);\r\n  }\r\n}\r\n\r\n// Fallback function for when API fails\r\nfunction createFallbackTicket(type: string, query?: string, errorMessage?: string): Ticket {\r\n  const fallbackMatches: Match[] = [\r\n    {\r\n      match: \"FCSB vs CFR Cluj\",\r\n      date: \"2025-07-10 20:00\",\r\n      sport: \"fotbal\",\r\n      bet_type: \"1X2\",\r\n      selection: \"1\",\r\n      odds: 2.10,\r\n      league: \"Liga 1 Romania\"\r\n    },\r\n    {\r\n      match: \"Real Madrid vs Barcelona\",\r\n      date: \"2025-07-11 21:00\",\r\n      sport: \"fotbal\",\r\n      bet_type: \"1X2\",\r\n      selection: \"X\",\r\n      odds: 3.20,\r\n      league: \"La Liga\"\r\n    },\r\n    {\r\n      match: \"Manchester City vs Liverpool\",\r\n      date: \"2025-07-12 17:30\",\r\n      sport: \"fotbal\",\r\n      bet_type: \"Over/Under\",\r\n      selection: \"Over 2.5\",\r\n      odds: 1.85,\r\n      league: \"Premier League\"\r\n    }\r\n  ];\r\n\r\n  const totalOdds = fallbackMatches.reduce((acc, match) => acc * match.odds, 1);\r\n\r\n  const explanation = errorMessage\r\n    ? `Bilet generat cu date simulate. Eroare API: ${errorMessage}. Încearcă din nou în câteva momente.`\r\n    : \"Bilet generat cu date simulate din cauza unei probleme temporare cu API-ul. Încearcă din nou în câteva momente.\";\r\n\r\n  return {\r\n    id: Date.now().toString(),\r\n    type,\r\n    matches: fallbackMatches,\r\n    totalOdds: parseFloat(totalOdds.toFixed(2)),\r\n    description: query ? \"🎯 **Bilet personalizat** - Generat special pentru tine!\" : getDescriptionForType(type),\r\n    confidence: \"mediu\",\r\n    explanation,\r\n    risk_level: \"mediu\"\r\n  };\r\n}\r\n\r\n// Format ticket for display\r\nexport function formatTicket(ticket: Ticket): string {\r\n  let formatted = `${ticket.description}\\n\\n`;\r\n\r\n  ticket.matches.forEach((match, index) => {\r\n    // Parse match string to get team names\r\n    const teams = match.match.split(' vs ');\r\n    const team1 = teams[0] || match.match;\r\n    const team2 = teams[1] || '';\r\n\r\n    formatted += `${index + 1}. **${team1}**${team2 ? ` vs **${team2}**` : ''}\\n`;\r\n    formatted += `   📍 ${match.league}`;\r\n    if (match.date) {\r\n      const date = new Date(match.date);\r\n      const timeStr = date.toLocaleTimeString('ro-RO', { hour: '2-digit', minute: '2-digit' });\r\n      formatted += ` | ⏰ ${timeStr}`;\r\n    }\r\n    formatted += `\\n   🎯 ${match.bet_type}: ${match.selection} | 💰 Cotă: ${match.odds}\\n\\n`;\r\n  });\r\n\r\n  formatted += `💎 **Cotă totală: ${ticket.totalOdds}**\\n`;\r\n  formatted += `📊 **Încredere AI: ${ticket.confidence}**`;\r\n\r\n  if (ticket.explanation) {\r\n    formatted += `\\n\\n📝 **Explicație:**\\n${ticket.explanation}`;\r\n  }\r\n\r\n  if (ticket.recommended_stake) {\r\n    formatted += `\\n\\n💰 **Miză recomandată: ${ticket.recommended_stake} RON**`;\r\n  }\r\n\r\n  if (ticket.potential_win) {\r\n    formatted += `\\n🎯 **Câștig potențial: ${ticket.potential_win} RON**`;\r\n  }\r\n\r\n  if (ticket.risk_level) {\r\n    const riskEmoji = ticket.risk_level === 'ridicat' ? '🔥' : ticket.risk_level === 'mediu' ? '⚖️' : '🛡️';\r\n    formatted += `\\n${riskEmoji} **Nivel risc: ${ticket.risk_level}**`;\r\n  }\r\n\r\n  return formatted;\r\n}\r\n"], "names": [], "mappings": "AAAA,gCAAgC;AAChC,0DAA0D;;;;;;AAyB1D,oBAAoB;AACpB,MAAM,eAAe;AA+CrB,4BAA4B;AAC5B,eAAe,sBAAsB,KAAa,EAAE,UAIhD,CAAC,CAAC;IACJ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,oBAAoB,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA,GAAG,OAAO;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAwB,MAAM,SAAS,IAAI;QACjD,OAAO,KAAK,OAAO;IACrB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,eAAe,eAAe,MAAc;IAC1C,MAAM,cAAc,IAAI,2CAA2C;IACnE,IAAI,WAAW;IAEf,MAAO,WAAW,YAAa;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,qBAAqB,EAAE,QAAQ;YAE5E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,MAAM,EAAE;YAC3D;YAEA,MAAM,aAAgC,MAAM,SAAS,IAAI;YAEzD,IAAI,WAAW,MAAM,KAAK,aAAa;gBACrC,8CAA8C;gBAC9C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,yCAAyC;gBACzC,IAAI,iBAAiB;gBACrB,MAAM,oBAAoB;gBAE1B,MAAO,iBAAiB,kBAAmB;oBACzC,IAAI;wBACF,MAAM,iBAAiB,MAAM,MAAM,GAAG,aAAa,qBAAqB,EAAE,QAAQ;wBAElF,IAAI,eAAe,EAAE,EAAE;4BACrB,OAAO,MAAM,eAAe,IAAI;wBAClC,OAAO,IAAI,eAAe,MAAM,KAAK,KAAK;4BACxC,QAAQ,IAAI,CAAC,CAAC,8BAA8B,EAAE,iBAAiB,EAAE,CAAC,EAAE,mBAAmB;4BACvF,IAAI,iBAAiB,oBAAoB,GAAG;gCAC1C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gCACjD;gCACA;4BACF;wBACF;wBAEA,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,eAAe,MAAM,EAAE;oBACjE,EAAE,OAAO,aAAa;wBACpB,IAAI,kBAAkB,oBAAoB,GAAG;4BAC3C,MAAM;wBACR;wBACA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;wBACjD;oBACF;gBACF;YACF,OAAO,IAAI,WAAW,MAAM,KAAK,UAAU;gBACzC,MAAM,IAAI,MAAM;YAClB;YAEA,kCAAkC;YAClC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,IAAI,YAAY,cAAc,GAAG;gBAC/B,MAAM;YACR;YACA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD;QACF;IACF;IAEA,MAAM,IAAI,MAAM;AAClB;AAEA,SAAS,2BAA2B,WAA8B,EAAE,IAAY;IAC9E,MAAM,cAAc,YAAY,YAAY;IAE5C,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB;QACA,SAAS,YAAY,OAAO;QAC5B,WAAW,YAAY,UAAU;QACjC,aAAa,sBAAsB;QACnC,YAAY,YAAY,UAAU;QAClC,aAAa,YAAY,WAAW;QACpC,mBAAmB,YAAY,iBAAiB;QAChD,eAAe,YAAY,aAAa;QACxC,YAAY,YAAY,UAAU;IACpC;AACF;AAEA,SAAS,sBAAsB,IAAY;IACzC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,SAAS,gBAAgB,IAAY;IACnC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,eAAe,yBAAyB,IAAY;IACzD,IAAI;QACF,MAAM,QAAQ,gBAAgB;QAC9B,MAAM,UAAkE,CAAC;QAEzE,qCAAqC;QACrC,OAAQ;YACN,KAAK;gBACH,QAAQ,QAAQ,GAAG,MAAM,6BAA6B;gBACtD;YACF,KAAK;gBACH,QAAQ,QAAQ,GAAG,KAAK,2BAA2B;gBACnD;YACF,KAAK;gBACH,QAAQ,KAAK,GAAG;gBAChB;YACF,KAAK;gBACH,QAAQ,MAAM,GAAG;gBACjB;QACJ;QAEA,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK,qBAAqB,EAAE,MAAM,cAAc,CAAC,EAAE;QAEhF,MAAM,SAAS,MAAM,sBAAsB,OAAO;QAClD,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QAE/C,MAAM,cAAc,MAAM,eAAe;QACzC,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,CAAC;QAExD,OAAO,2BAA2B,aAAa;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,EAAE;QACpD,gDAAgD;QAChD,OAAO,qBAAqB,MAAM,WAAW,MAAM,OAAO;IAC5D;AACF;AAGO,eAAe,mBAAmB,KAAa;IACpD,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,MAAM,CAAC,CAAC;QAE9D,MAAM,SAAS,MAAM,sBAAsB;QAC3C,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,QAAQ;QAEpD,MAAM,cAAc,MAAM,eAAe;QACzC,QAAQ,GAAG,CAAC,CAAC,4BAA4B,CAAC;QAE1C,OAAO,2BAA2B,aAAa;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,gDAAgD;QAChD,OAAO,qBAAqB,QAAQ,OAAO,MAAM,OAAO;IAC1D;AACF;AAEA,uCAAuC;AACvC,SAAS,qBAAqB,IAAY,EAAE,KAAc,EAAE,YAAqB;IAC/E,MAAM,kBAA2B;QAC/B;YACE,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,MAAM;YACN,QAAQ;QACV;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,MAAM;YACN,QAAQ;QACV;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,MAAM;YACN,QAAQ;QACV;KACD;IAED,MAAM,YAAY,gBAAgB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,IAAI,EAAE;IAE3E,MAAM,cAAc,eAChB,CAAC,4CAA4C,EAAE,aAAa,qCAAqC,CAAC,GAClG;IAEJ,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB;QACA,SAAS;QACT,WAAW,WAAW,UAAU,OAAO,CAAC;QACxC,aAAa,QAAQ,6DAA6D,sBAAsB;QACxG,YAAY;QACZ;QACA,YAAY;IACd;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI,YAAY,GAAG,OAAO,WAAW,CAAC,IAAI,CAAC;IAE3C,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;QAC7B,uCAAuC;QACvC,MAAM,QAAQ,MAAM,KAAK,CAAC,KAAK,CAAC;QAChC,MAAM,QAAQ,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK;QACrC,MAAM,QAAQ,KAAK,CAAC,EAAE,IAAI;QAE1B,aAAa,GAAG,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;QAC7E,aAAa,CAAC,MAAM,EAAE,MAAM,MAAM,EAAE;QACpC,IAAI,MAAM,IAAI,EAAE;YACd,MAAM,OAAO,IAAI,KAAK,MAAM,IAAI;YAChC,MAAM,UAAU,KAAK,kBAAkB,CAAC,SAAS;gBAAE,MAAM;gBAAW,QAAQ;YAAU;YACtF,aAAa,CAAC,KAAK,EAAE,SAAS;QAChC;QACA,aAAa,CAAC,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,MAAM,SAAS,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC;IAC3F;IAEA,aAAa,CAAC,kBAAkB,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC;IACxD,aAAa,CAAC,mBAAmB,EAAE,OAAO,UAAU,CAAC,EAAE,CAAC;IAExD,IAAI,OAAO,WAAW,EAAE;QACtB,aAAa,CAAC,wBAAwB,EAAE,OAAO,WAAW,EAAE;IAC9D;IAEA,IAAI,OAAO,iBAAiB,EAAE;QAC5B,aAAa,CAAC,2BAA2B,EAAE,OAAO,iBAAiB,CAAC,MAAM,CAAC;IAC7E;IAEA,IAAI,OAAO,aAAa,EAAE;QACxB,aAAa,CAAC,yBAAyB,EAAE,OAAO,aAAa,CAAC,MAAM,CAAC;IACvE;IAEA,IAAI,OAAO,UAAU,EAAE;QACrB,MAAM,YAAY,OAAO,UAAU,KAAK,YAAY,OAAO,OAAO,UAAU,KAAK,UAAU,OAAO;QAClG,aAAa,CAAC,EAAE,EAAE,UAAU,eAAe,EAAE,OAAO,UAAU,CAAC,EAAE,CAAC;IACpE;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { generatePredefinedTicket, generateChatTicket, formatTicket, type Ticket } from '@/lib/ticketGenerator';\r\n\r\ninterface Message {\r\n  id: string;\r\n  text: string;\r\n  isUser: boolean;\r\n  ticketId?: string;\r\n  shareUrl?: string;\r\n}\r\n\r\nexport default function Home() {\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [inputValue, setInputValue] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [loadingTime, setLoadingTime] = useState(0);\r\n  const [loadingMessage, setLoadingMessage] = useState('');\r\n  const [isFullscreen, setIsFullscreen] = useState(false);\r\n  const timerRef = useRef<NodeJS.Timeout | null>(null);\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Timer helper functions\r\n  const startTimer = (initialMessage: string) => {\r\n    setLoadingTime(0);\r\n    setLoadingMessage(initialMessage);\r\n\r\n    timerRef.current = setInterval(() => {\r\n      setLoadingTime(prev => {\r\n        const newTime = prev + 1;\r\n\r\n        // Update loading message based on time elapsed\r\n        if (newTime <= 5) {\r\n          setLoadingMessage('🤖 AI-ul analizează cererea ta...');\r\n        } else if (newTime <= 10) {\r\n          setLoadingMessage('📊 Colectez date despre meciuri...');\r\n        } else if (newTime <= 15) {\r\n          setLoadingMessage('⚽ Analizez statistici și cote...');\r\n        } else if (newTime <= 20) {\r\n          setLoadingMessage('🎯 Calculez predicții optimale...');\r\n        } else if (newTime <= 25) {\r\n          setLoadingMessage('💎 Finalizez biletul tău...');\r\n        } else {\r\n          setLoadingMessage('⏳ Încă lucrez la biletul perfect...');\r\n        }\r\n\r\n        return newTime;\r\n      });\r\n    }, 1000);\r\n  };\r\n\r\n  const stopTimer = () => {\r\n    if (timerRef.current) {\r\n      clearInterval(timerRef.current);\r\n      timerRef.current = null;\r\n    }\r\n    setLoadingTime(0);\r\n    setLoadingMessage('');\r\n  };\r\n\r\n  // Auto-scroll to bottom when messages change\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages, isLoading]);\r\n\r\n  // Mobile fullscreen helpers\r\n  const enterFullscreen = () => {\r\n    // Check if we're on mobile (width <= 768px) or if we're in a mobile viewport\r\n    const isMobile = window.innerWidth <= 768 ||\r\n                     /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\r\n\r\n    if (isMobile) {\r\n      console.log('🔄 Entering fullscreen mode for mobile');\r\n      setIsFullscreen(true);\r\n      document.body.style.overflow = 'hidden';\r\n\r\n      // Force scroll to chat area after a brief delay to ensure DOM is updated\r\n      setTimeout(() => {\r\n        chatContainerRef.current?.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'start'\r\n        });\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n  const exitFullscreen = () => {\r\n    console.log('🔄 Exiting fullscreen mode');\r\n    setIsFullscreen(false);\r\n    document.body.style.overflow = 'auto';\r\n  };\r\n\r\n  // Cleanup timer on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (timerRef.current) {\r\n        clearInterval(timerRef.current);\r\n      }\r\n      // Cleanup fullscreen on unmount\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, []);\r\n\r\n  // Generate shareable URL for ticket\r\n  const generateShareUrl = async (ticket: Ticket): Promise<string> => {\r\n    try {\r\n      const response = await fetch('/api/tickets', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ ticket }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to create shared ticket: ${response.status}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      // Store ticket in localStorage as backup\r\n      const shareId = data.shareId;\r\n      if (shareId) {\r\n        localStorage.setItem(`ticket_${shareId}`, JSON.stringify({\r\n          ticket,\r\n          createdAt: new Date().toISOString(),\r\n          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days\r\n        }));\r\n      }\r\n\r\n      return data.shareUrl;\r\n    } catch (error) {\r\n      console.error('Error creating share URL:', error);\r\n\r\n      // Fallback: create local-only shareable ticket\r\n      const ticketId = `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n      localStorage.setItem(`ticket_${ticketId}`, JSON.stringify({\r\n        ticket,\r\n        createdAt: new Date().toISOString(),\r\n        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n        localOnly: true\r\n      }));\r\n\r\n      return `${window.location.origin}/bilet/${ticketId}`;\r\n    }\r\n  };\r\n\r\n  // Copy share URL to clipboard\r\n  const copyToClipboard = async (text: string) => {\r\n    try {\r\n      await navigator.clipboard.writeText(text);\r\n      alert('Link copiat în clipboard!');\r\n    } catch (err) {\r\n      console.error('Failed to copy: ', err);\r\n      // Fallback for older browsers\r\n      const textArea = document.createElement('textarea');\r\n      textArea.value = text;\r\n      document.body.appendChild(textArea);\r\n      textArea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textArea);\r\n      alert('Link copiat în clipboard!');\r\n    }\r\n  };\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!inputValue.trim()) return;\r\n\r\n    // Enter fullscreen on mobile\r\n    enterFullscreen();\r\n\r\n    const userMessage = {\r\n      id: Date.now().toString(),\r\n      text: inputValue,\r\n      isUser: true\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    const queryText = inputValue;\r\n    setInputValue('');\r\n    setIsLoading(true);\r\n    startTimer('🤖 AI-ul analizează cererea ta...');\r\n\r\n    try {\r\n      // Generate ticket using real API\r\n      const ticket = await generateChatTicket(queryText);\r\n      const shareUrl = await generateShareUrl(ticket);\r\n      const aiResponse = {\r\n        id: (Date.now() + 1).toString(),\r\n        text: formatTicket(ticket),\r\n        isUser: false,\r\n        ticketId: ticket.id,\r\n        shareUrl: shareUrl\r\n      };\r\n      setMessages(prev => [...prev, aiResponse]);\r\n    } catch (error) {\r\n      console.error('Error generating ticket:', error);\r\n      const errorResponse = {\r\n        id: (Date.now() + 1).toString(),\r\n        text: \"❌ Ne pare rău, a apărut o problemă la generarea biletului. Te rugăm să încerci din nou.\",\r\n        isUser: false\r\n      };\r\n      setMessages(prev => [...prev, errorResponse]);\r\n    } finally {\r\n      stopTimer();\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePredefinedButton = async (type: string) => {\r\n    // Enter fullscreen on mobile\r\n    enterFullscreen();\r\n\r\n    // Add a system message to show which button was pressed\r\n    const getButtonDescription = (buttonType: string) => {\r\n      switch (buttonType) {\r\n        case 'curajos':\r\n          return '🔥 Cel mai curajos bilet';\r\n        case 'sigur':\r\n          return '🛡️ Cel mai sigur bilet';\r\n        case 'ziua':\r\n          return '⭐ Recomandarea zilei';\r\n        case 'saptamana':\r\n          return '👑 Recomandarea săptămânii';\r\n        default:\r\n          return '🎯 Bilet personalizat';\r\n      }\r\n    };\r\n\r\n    // Add system message to chat\r\n    const systemMessage = {\r\n      id: Date.now().toString(),\r\n      text: `Ai selectat: ${getButtonDescription(type)}`,\r\n      isUser: true\r\n    };\r\n    setMessages(prev => [...prev, systemMessage]);\r\n\r\n    setIsLoading(true);\r\n\r\n    // Set specific timer message based on button type\r\n    const getTimerMessage = (buttonType: string) => {\r\n      switch (buttonType) {\r\n        case 'curajos':\r\n          return '🔥 Caut cele mai curajoase pariuri...';\r\n        case 'sigur':\r\n          return '🛡️ Analizez pariurile sigure...';\r\n        case 'ziua':\r\n          return '⭐ Pregătesc recomandarea zilei...';\r\n        case 'saptamana':\r\n          return '👑 Creez biletul premium al săptămânii...';\r\n        default:\r\n          return '🤖 AI-ul lucrează la biletul tău...';\r\n      }\r\n    };\r\n\r\n    startTimer(getTimerMessage(type));\r\n\r\n    try {\r\n      // Generate ticket using real API\r\n      const ticket = await generatePredefinedTicket(type);\r\n      const shareUrl = await generateShareUrl(ticket);\r\n      const response = {\r\n        id: Date.now().toString(),\r\n        text: formatTicket(ticket),\r\n        isUser: false,\r\n        ticketId: ticket.id,\r\n        shareUrl: shareUrl\r\n      };\r\n      setMessages(prev => [...prev, response]);\r\n    } catch (error) {\r\n      console.error('Error generating predefined ticket:', error);\r\n      const errorResponse = {\r\n        id: Date.now().toString(),\r\n        text: \"❌ Ne pare rău, a apărut o problemă la generarea biletului. Te rugăm să încerci din nou.\",\r\n        isUser: false\r\n      };\r\n      setMessages(prev => [...prev, errorResponse]);\r\n    } finally {\r\n      stopTimer();\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-darker-surface to-dark-surface\">\r\n      {/* Header */}\r\n      <header className=\"border-b border-dark-surface bg-darker-surface/80 backdrop-blur-sm\">\r\n        <div className=\"container mx-auto px-4 py-4 sm:py-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h1 className=\"text-2xl sm:text-3xl font-bold gradient-text\">\r\n              AIpariat\r\n            </h1>\r\n            <div className=\"flex items-center space-x-2 text-xs sm:text-sm text-gray-400\">\r\n              <span className=\"w-2 h-2 bg-primary rounded-full animate-pulse\"></span>\r\n              <span className=\"hidden sm:inline\">AI Activ</span>\r\n              <span className=\"sm:hidden\">AI</span>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-gray-300 mt-2 text-sm sm:text-base\">\r\n            Toate casele de pariuri folosesc algoritmi pentru garantarea profitului. Am creat AIpariat pentru egalizarea șanselor.\r\n          </p>\r\n        </div>\r\n      </header>\r\n\r\n      <div className=\"container mx-auto px-4 py-4 sm:py-8 max-w-6xl\">\r\n        <div className=\"grid lg:grid-cols-3 gap-6 lg:gap-8\">\r\n          {/* Predefined Buttons */}\r\n          <div className=\"lg:col-span-1 order-2 lg:order-1\">\r\n            <h2 className=\"text-lg sm:text-xl font-semibold mb-4 sm:mb-6 text-primary\">\r\n              Bilete Rapide\r\n            </h2>\r\n            <div className=\"space-y-3 sm:space-y-4\">\r\n              <button\r\n                onClick={() => handlePredefinedButton('curajos')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-casino-red to-secondary rounded-lg border border-casino-red/30 hover:border-casino-red/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">🔥 Cel mai curajos bilet</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Cote mari, risc ridicat</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('sigur')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-casino-green to-primary rounded-lg border border-casino-green/30 hover:border-casino-green/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">🛡️ Cel mai sigur bilet</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Cote mici, șanse mari</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('ziua')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-ai-blue to-ai-purple rounded-lg border border-ai-blue/30 hover:border-ai-blue/60 transition-all duration-300 glow-ai hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">⭐ Recomandarea zilei</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Selecția AI pentru astăzi</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('saptamana')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-accent to-secondary rounded-lg border border-accent/30 hover:border-accent/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-black text-sm sm:text-base\">👑 Recomandarea săptămânii</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-800 mt-1\">Biletul premium al săptămânii</div>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Chat Interface */}\r\n          <div\r\n            ref={chatContainerRef}\r\n            className={`lg:col-span-2 order-1 lg:order-2 ${\r\n              isFullscreen\r\n                ? 'fixed inset-0 z-50 bg-dark-background p-4 flex flex-col'\r\n                : ''\r\n            }`}\r\n          >\r\n            <div className={`flex items-center justify-between mb-4 sm:mb-6 ${isFullscreen ? 'flex-shrink-0' : ''}`}>\r\n              <h2 className=\"text-lg sm:text-xl font-semibold text-primary\">\r\n                Chat cu AI-ul nostru\r\n              </h2>\r\n              {isFullscreen && (\r\n                <button\r\n                  onClick={exitFullscreen}\r\n                  className=\"text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-800 transition-colors\"\r\n                  aria-label=\"Ieși din fullscreen\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              )}\r\n            </div>\r\n\r\n            {/* Chat Messages */}\r\n            <div className={`bg-dark-surface rounded-lg border border-primary/20 overflow-y-auto p-3 sm:p-4 mb-4 ${\r\n              isFullscreen\r\n                ? 'flex-1 h-full'\r\n                : 'h-64 sm:h-80 lg:h-96'\r\n            }`}>\r\n              {messages.length === 0 ? (\r\n                <div className=\"flex items-center justify-center h-full text-gray-400\">\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-4xl mb-4\">🤖</div>\r\n                    <p>Salut! Sunt AIpariat, expertul în pariuri sportive care folosește cele mai avansate analize AI.</p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-4\">\r\n                  {messages.map((message) => (\r\n                    <div\r\n                      key={message.id}\r\n                      className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}\r\n                    >\r\n                      <div\r\n                        className={`max-w-[280px] sm:max-w-xs lg:max-w-md px-3 sm:px-4 py-2 rounded-lg ${\r\n                          message.isUser\r\n                            ? 'bg-primary text-black'\r\n                            : 'bg-darker-surface text-white border border-primary/30'\r\n                        }`}\r\n                      >\r\n                        <div className=\"whitespace-pre-line text-sm\">\r\n                          {message.text}\r\n                        </div>\r\n                        {!message.isUser && message.shareUrl && (\r\n                          <div className=\"mt-3 pt-3 border-t border-primary/20\">\r\n                            <button\r\n                              onClick={() => copyToClipboard(message.shareUrl!)}\r\n                              className=\"flex items-center space-x-2 text-xs bg-primary/20 hover:bg-primary/30 px-3 py-1 rounded transition-colors\"\r\n                            >\r\n                              <span>🔗</span>\r\n                              <span>Partajează biletul</span>\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                  {isLoading && (\r\n                    <div className=\"flex justify-start\">\r\n                      <div className=\"bg-darker-surface text-white border border-primary/30 px-4 py-3 rounded-lg max-w-[320px] sm:max-w-sm\">\r\n                        {/* Timer and Progress Bar */}\r\n                        <div className=\"flex items-center justify-between mb-3\">\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <div className=\"w-3 h-3 bg-primary rounded-full animate-pulse\"></div>\r\n                            <span className=\"text-xs font-medium text-primary\">\r\n                              {Math.floor(loadingTime / 60)}:{(loadingTime % 60).toString().padStart(2, '0')}\r\n                            </span>\r\n                          </div>\r\n                          <div className=\"text-xs text-gray-400\">\r\n                            AI lucrează...\r\n                          </div>\r\n                        </div>\r\n\r\n                        {/* Progress Bar */}\r\n                        <div className=\"w-full bg-gray-700 rounded-full h-1.5 mb-3\">\r\n                          <div\r\n                            className=\"bg-gradient-to-r from-primary to-secondary h-1.5 rounded-full transition-all duration-1000 ease-out\"\r\n                            style={{\r\n                              width: `${Math.min((loadingTime / 30) * 100, 95)}%`\r\n                            }}\r\n                          ></div>\r\n                        </div>\r\n\r\n                        {/* Loading Message */}\r\n                        <div className=\"text-sm\">\r\n                          {loadingMessage}\r\n                        </div>\r\n\r\n                        {/* Animated Dots */}\r\n                        <div className=\"flex items-center space-x-1 mt-2\">\r\n                          <div className=\"w-1.5 h-1.5 bg-primary rounded-full animate-bounce\"></div>\r\n                          <div className=\"w-1.5 h-1.5 bg-primary rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\r\n                          <div className=\"w-1.5 h-1.5 bg-primary rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\r\n                          <div className=\"w-1.5 h-1.5 bg-primary rounded-full animate-bounce\" style={{animationDelay: '0.3s'}}></div>\r\n                        </div>\r\n\r\n                        {/* Helpful tip after 15 seconds */}\r\n                        {loadingTime > 15 && (\r\n                          <div className=\"mt-3 pt-3 border-t border-primary/20 text-xs text-gray-400\">\r\n                            💡 AI-ul folosește date în timp real pentru cele mai precise predicții\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n              <div ref={messagesEndRef} />\r\n            </div>\r\n\r\n            {/* Chat Input */}\r\n            <div className={`flex flex-col sm:flex-row gap-2 sm:gap-2 ${isFullscreen ? 'flex-shrink-0' : ''}`}>\r\n              <input\r\n                type=\"text\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}\r\n                onFocus={enterFullscreen}\r\n                placeholder=\"Descrie biletul dorit\"\r\n                disabled={isLoading}\r\n                className=\"flex-1 px-3 sm:px-4 py-3 bg-darker-surface border border-primary/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary disabled:opacity-50 text-sm sm:text-base\"\r\n              />\r\n              <button\r\n                onClick={handleSendMessage}\r\n                disabled={isLoading || !inputValue.trim()}\r\n                className=\"px-4 sm:px-6 py-3 bg-primary text-black font-semibold rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base whitespace-nowrap\"\r\n              >\r\n                Trimite\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <footer className=\"mt-8 sm:mt-16 text-center text-gray-400 text-xs sm:text-sm\">\r\n          <div className=\"border-t border-dark-surface pt-6 sm:pt-8\">\r\n            <p>&copy; 2025 AIpariat. Toate drepturile rezervate.</p>\r\n            <p className=\"mt-2\">\r\n              Această platformă are exclusiv rol consultativ și nu poate fi folosită pentru plasarea pariurilor sportive.\r\n            </p>\r\n          </div>\r\n        </footer>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAae,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAC/C,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEhD,yBAAyB;IACzB,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,kBAAkB;QAElB,SAAS,OAAO,GAAG,YAAY;YAC7B,eAAe,CAAA;gBACb,MAAM,UAAU,OAAO;gBAEvB,+CAA+C;gBAC/C,IAAI,WAAW,GAAG;oBAChB,kBAAkB;gBACpB,OAAO,IAAI,WAAW,IAAI;oBACxB,kBAAkB;gBACpB,OAAO,IAAI,WAAW,IAAI;oBACxB,kBAAkB;gBACpB,OAAO,IAAI,WAAW,IAAI;oBACxB,kBAAkB;gBACpB,OAAO,IAAI,WAAW,IAAI;oBACxB,kBAAkB;gBACpB,OAAO;oBACL,kBAAkB;gBACpB;gBAEA,OAAO;YACT;QACF,GAAG;IACL;IAEA,MAAM,YAAY;QAChB,IAAI,SAAS,OAAO,EAAE;YACpB,cAAc,SAAS,OAAO;YAC9B,SAAS,OAAO,GAAG;QACrB;QACA,eAAe;QACf,kBAAkB;IACpB;IAEA,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,4BAA4B;IAC5B,MAAM,kBAAkB;QACtB,6EAA6E;QAC7E,MAAM,WAAW,OAAO,UAAU,IAAI,OACrB,iEAAiE,IAAI,CAAC,UAAU,SAAS;QAE1G,IAAI,UAAU;YACZ,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAE/B,yEAAyE;YACzE,WAAW;gBACT,iBAAiB,OAAO,EAAE,eAAe;oBACvC,UAAU;oBACV,OAAO;gBACT;YACF,GAAG;QACL;IACF;IAEA,MAAM,iBAAiB;QACrB,QAAQ,GAAG,CAAC;QACZ,gBAAgB;QAChB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IACjC;IAEA,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,SAAS,OAAO,EAAE;gBACpB,cAAc,SAAS,OAAO;YAChC;YACA,gCAAgC;YAChC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG,EAAE;IAEL,oCAAoC;IACpC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,MAAM,EAAE;YACtE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,yCAAyC;YACzC,MAAM,UAAU,KAAK,OAAO;YAC5B,IAAI,SAAS;gBACX,aAAa,OAAO,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,SAAS,CAAC;oBACvD;oBACA,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,SAAS;gBACnF;YACF;YAEA,OAAO,KAAK,QAAQ;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,+CAA+C;YAC/C,MAAM,WAAW,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACjF,aAAa,OAAO,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,SAAS,CAAC;gBACxD;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACrE,WAAW;YACb;YAEA,OAAO,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,UAAU;QACtD;IACF;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oBAAoB;YAClC,8BAA8B;YAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,MAAM;YACf,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,6BAA6B;QAC7B;QAEA,MAAM,cAAc;YAClB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,QAAQ;QACV;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,MAAM,YAAY;QAClB,cAAc;QACd,aAAa;QACb,WAAW;QAEX,IAAI;YACF,iCAAiC;YACjC,MAAM,SAAS,MAAM,CAAA,GAAA,6HAAA,CAAA,qBAAkB,AAAD,EAAE;YACxC,MAAM,WAAW,MAAM,iBAAiB;YACxC,MAAM,aAAa;gBACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,QAAQ;gBACR,UAAU,OAAO,EAAE;gBACnB,UAAU;YACZ;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,gBAAgB;gBACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,QAAQ;YACV;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAc;QAC9C,SAAU;YACR;YACA,aAAa;QACf;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,6BAA6B;QAC7B;QAEA,wDAAwD;QACxD,MAAM,uBAAuB,CAAC;YAC5B,OAAQ;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,6BAA6B;QAC7B,MAAM,gBAAgB;YACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM,CAAC,aAAa,EAAE,qBAAqB,OAAO;YAClD,QAAQ;QACV;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAc;QAE5C,aAAa;QAEb,kDAAkD;QAClD,MAAM,kBAAkB,CAAC;YACvB,OAAQ;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,WAAW,gBAAgB;QAE3B,IAAI;YACF,iCAAiC;YACjC,MAAM,SAAS,MAAM,CAAA,GAAA,6HAAA,CAAA,2BAAwB,AAAD,EAAE;YAC9C,MAAM,WAAW,MAAM,iBAAiB;YACxC,MAAM,WAAW;gBACf,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,QAAQ;gBACR,UAAU,OAAO,EAAE;gBACnB,UAAU;YACZ;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAS;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,gBAAgB;gBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,QAAQ;YACV;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAc;QAC9C,SAAU;YACR;YACA,aAAa;QACf;IACF;IAIA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAG7D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,8OAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;;;;;;;sCAGhC,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAM3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAG3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,8OAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,8OAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,8OAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/D,8OAAC;gCACC,KAAK;gCACL,WAAW,CAAC,iCAAiC,EAC3C,eACI,4DACA,IACJ;;kDAEF,8OAAC;wCAAI,WAAW,CAAC,+CAA+C,EAAE,eAAe,kBAAkB,IAAI;;0DACrG,8OAAC;gDAAG,WAAU;0DAAgD;;;;;;4CAG7D,8BACC,8OAAC;gDACC,SAAS;gDACT,WAAU;gDACV,cAAW;0DAEX,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kDAO7E,8OAAC;wCAAI,WAAW,CAAC,oFAAoF,EACnG,eACI,kBACA,wBACJ;;4CACC,SAAS,MAAM,KAAK,kBACnB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;sEAAE;;;;;;;;;;;;;;;;qEAIP,8OAAC;gDAAI,WAAU;;oDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4DAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,gBAAgB,iBAAiB;sEAErE,cAAA,8OAAC;gEACC,WAAW,CAAC,mEAAmE,EAC7E,QAAQ,MAAM,GACV,0BACA,yDACJ;;kFAEF,8OAAC;wEAAI,WAAU;kFACZ,QAAQ,IAAI;;;;;;oEAEd,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,kBAClC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EACC,SAAS,IAAM,gBAAgB,QAAQ,QAAQ;4EAC/C,WAAU;;8FAEV,8OAAC;8FAAK;;;;;;8FACN,8OAAC;8FAAK;;;;;;;;;;;;;;;;;;;;;;;2DApBT,QAAQ,EAAE;;;;;oDA2BlB,2BACC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EAEb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;;;;;8FACf,8OAAC;oFAAK,WAAU;;wFACb,KAAK,KAAK,CAAC,cAAc;wFAAI;wFAAE,CAAC,cAAc,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;;;sFAG9E,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAMzC,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,WAAU;wEACV,OAAO;4EACL,OAAO,GAAG,KAAK,GAAG,CAAC,AAAC,cAAc,KAAM,KAAK,IAAI,CAAC,CAAC;wEACrD;;;;;;;;;;;8EAKJ,8OAAC;oEAAI,WAAU;8EACZ;;;;;;8EAIH,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAI,WAAU;4EAAqD,OAAO;gFAAC,gBAAgB;4EAAM;;;;;;sFAClG,8OAAC;4EAAI,WAAU;4EAAqD,OAAO;gFAAC,gBAAgB;4EAAM;;;;;;sFAClG,8OAAC;4EAAI,WAAU;4EAAqD,OAAO;gFAAC,gBAAgB;4EAAM;;;;;;;;;;;;gEAInG,cAAc,oBACb,8OAAC;oEAAI,WAAU;8EAA6D;;;;;;;;;;;;;;;;;;;;;;;0DASxF,8OAAC;gDAAI,KAAK;;;;;;;;;;;;kDAIZ,8OAAC;wCAAI,WAAW,CAAC,yCAAyC,EAAE,eAAe,kBAAkB,IAAI;;0DAC/F,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;gDACxC,SAAS;gDACT,aAAY;gDACZ,UAAU;gDACV,WAAU;;;;;;0DAEZ,8OAAC;gDACC,SAAS;gDACT,UAAU,aAAa,CAAC,WAAW,IAAI;gDACvC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC", "debugId": null}}, {"offset": {"line": 1191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}