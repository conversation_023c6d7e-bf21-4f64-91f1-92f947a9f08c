{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/src/lib/ticketGenerator.ts"], "sourcesContent": ["// Ticket Generator for AIpariat\r\n// Generates realistic football betting tickets with Romanian teams and international matches\r\n\r\nexport interface Match {\r\n  team1: string;\r\n  team2: string;\r\n  prediction: string;\r\n  odds: string;\r\n  league: string;\r\n  time?: string;\r\n}\r\n\r\nexport interface Ticket {\r\n  id: string;\r\n  type: string;\r\n  matches: Match[];\r\n  totalOdds: number;\r\n  description: string;\r\n  confidence: number;\r\n}\r\n\r\n// Romanian teams and leagues\r\nconst romanianTeams = [\r\n  \"FCSB\", \"CFR Cluj\", \"Universitatea Craiova\", \"Rapid București\", \r\n  \"Sepsi OSK\", \"UTA Arad\", \"Farul Constanța\", \"Voluntari\",\r\n  \"Dinamo București\", \"Petrolul Ploiești\", \"Hermannstadt\", \"Botoșani\"\r\n];\r\n\r\n// International teams\r\nconst internationalTeams = [\r\n  \"Real Madrid\", \"Barcelona\", \"Manchester City\", \"Liverpool\", \r\n  \"Bayern Munich\", \"PSG\", \"Juventus\", \"Inter Milan\", \"AC Milan\",\r\n  \"Arsenal\", \"Chelsea\", \"Tottenham\", \"Manchester United\",\r\n  \"Atletico Madrid\", \"Borussia Dortmund\", \"RB Leipzig\",\r\n  \"Napoli\", \"Roma\", \"Lazio\", \"Atalanta\", \"Brighton\", \"Newcastle\"\r\n];\r\n\r\nconst leagues = [\r\n  \"Liga 1 România\", \"Premier League\", \"La Liga\", \"Serie A\", \r\n  \"Bundesliga\", \"Ligue 1\", \"Champions League\", \"Europa League\"\r\n];\r\n\r\n// Prediction types with Romanian explanations\r\nconst predictionTypes = {\r\n  \"1\": \"Victorie gazdă\",\r\n  \"X\": \"Egalitate\", \r\n  \"2\": \"Victorie oaspete\",\r\n  \"1X\": \"Gazdă sau egal\",\r\n  \"X2\": \"Oaspete sau egal\",\r\n  \"12\": \"Victorie (fără egal)\",\r\n  \"Over 2.5\": \"Peste 2.5 goluri\",\r\n  \"Under 2.5\": \"Sub 2.5 goluri\",\r\n  \"BTTS\": \"Ambele echipe marchează\"\r\n};\r\n\r\n// Generate random odds based on prediction type\r\nfunction generateOdds(prediction: string): string {\r\n  const oddsRanges: { [key: string]: [number, number] } = {\r\n    \"1\": [1.20, 3.50],\r\n    \"X\": [2.80, 4.20],\r\n    \"2\": [1.15, 4.00],\r\n    \"1X\": [1.10, 1.80],\r\n    \"X2\": [1.05, 2.20],\r\n    \"12\": [1.05, 1.40],\r\n    \"Over 2.5\": [1.40, 2.20],\r\n    \"Under 2.5\": [1.50, 2.80],\r\n    \"BTTS\": [1.60, 2.40]\r\n  };\r\n\r\n  const [min, max] = oddsRanges[prediction] || [1.50, 3.00];\r\n  const odds = Math.random() * (max - min) + min;\r\n  return odds.toFixed(2);\r\n}\r\n\r\n// Generate a random match\r\nfunction generateMatch(useRomanianTeams: boolean = false): Match {\r\n  const teams = useRomanianTeams ? romanianTeams : internationalTeams;\r\n  const league = useRomanianTeams ? \"Liga 1 România\" : leagues[Math.floor(Math.random() * (leagues.length - 1)) + 1];\r\n  \r\n  const team1 = teams[Math.floor(Math.random() * teams.length)];\r\n  let team2 = teams[Math.floor(Math.random() * teams.length)];\r\n  while (team2 === team1) {\r\n    team2 = teams[Math.floor(Math.random() * teams.length)];\r\n  }\r\n\r\n  const predictions = Object.keys(predictionTypes);\r\n  const prediction = predictions[Math.floor(Math.random() * predictions.length)];\r\n  const odds = generateOdds(prediction);\r\n\r\n  return {\r\n    team1,\r\n    team2,\r\n    prediction,\r\n    odds,\r\n    league,\r\n    time: `${Math.floor(Math.random() * 24)}:${Math.floor(Math.random() * 60).toString().padStart(2, '0')}`\r\n  };\r\n}\r\n\r\n// Generate predefined tickets\r\nexport function generatePredefinedTicket(type: string): Ticket {\r\n  let matches: Match[] = [];\r\n  let description = \"\";\r\n  let confidence = 0;\r\n\r\n  switch (type) {\r\n    case \"curajos\":\r\n      // High odds, risky bets\r\n      matches = [\r\n        generateMatch(),\r\n        generateMatch(),\r\n        generateMatch(true) // Include Romanian match\r\n      ];\r\n      // Adjust odds to be higher\r\n      matches = matches.map(match => ({\r\n        ...match,\r\n        odds: (parseFloat(match.odds) * 1.5 + 1).toFixed(2)\r\n      }));\r\n      description = \"🔥 **Cel mai curajos bilet** - Cote mari pentru câștiguri spectaculoase!\";\r\n      confidence = 25;\r\n      break;\r\n\r\n    case \"sigur\":\r\n      // Low odds, safe bets\r\n      matches = [\r\n        generateMatch(),\r\n        generateMatch(),\r\n        generateMatch()\r\n      ];\r\n      // Adjust odds to be lower (safer)\r\n      matches = matches.map(match => ({\r\n        ...match,\r\n        prediction: Math.random() > 0.7 ? \"1\" : match.prediction, // More home wins\r\n        odds: Math.max(1.10, parseFloat(match.odds) * 0.6).toFixed(2)\r\n      }));\r\n      description = \"🛡️ **Cel mai sigur bilet** - Selecții cu șanse mari de câștig!\";\r\n      confidence = 85;\r\n      break;\r\n\r\n    case \"ziua\":\r\n      // Balanced daily recommendation\r\n      matches = [\r\n        generateMatch(),\r\n        generateMatch(true), // Romanian match\r\n        generateMatch()\r\n      ];\r\n      description = \"⭐ **Recomandarea zilei** - Analiza AI pentru cele mai bune oportunități!\";\r\n      confidence = 65;\r\n      break;\r\n\r\n    case \"saptamana\":\r\n      // Premium weekly recommendation\r\n      matches = [\r\n        generateMatch(),\r\n        generateMatch(),\r\n        generateMatch(),\r\n        generateMatch(true) // Include Romanian match\r\n      ];\r\n      description = \"👑 **Recomandarea săptămânii** - Biletul premium analizat în detaliu!\";\r\n      confidence = 75;\r\n      break;\r\n\r\n    default:\r\n      matches = [generateMatch(), generateMatch(), generateMatch()];\r\n      description = \"🎯 Bilet generat de AI\";\r\n      confidence = 60;\r\n  }\r\n\r\n  const totalOdds = matches.reduce((acc, match) => acc * parseFloat(match.odds), 1);\r\n\r\n  return {\r\n    id: Date.now().toString(),\r\n    type,\r\n    matches,\r\n    totalOdds: parseFloat(totalOdds.toFixed(2)),\r\n    description,\r\n    confidence\r\n  };\r\n}\r\n\r\n// Generate ticket based on user query\r\nexport function generateChatTicket(query: string): Ticket {\r\n  const lowerQuery = query.toLowerCase();\r\n  let matches: Match[] = [];\r\n  let description = \"\";\r\n  const confidence = 60;\r\n\r\n  // Analyze query for specific requests\r\n  if (lowerQuery.includes(\"sigur\") || lowerQuery.includes(\"safe\")) {\r\n    return generatePredefinedTicket(\"sigur\");\r\n  } else if (lowerQuery.includes(\"curajos\") || lowerQuery.includes(\"risc\")) {\r\n    return generatePredefinedTicket(\"curajos\");\r\n  } else if (lowerQuery.includes(\"român\") || lowerQuery.includes(\"liga 1\")) {\r\n    // Romanian league focus\r\n    matches = [\r\n      generateMatch(true),\r\n      generateMatch(true),\r\n      generateMatch(true)\r\n    ];\r\n    description = \"🇷🇴 **Bilet Liga 1 România** - Focalizat pe campionatul nostru!\";\r\n  } else if (lowerQuery.includes(\"champions\") || lowerQuery.includes(\"europa\")) {\r\n    // European competitions\r\n    matches = [\r\n      { ...generateMatch(), league: \"Champions League\" },\r\n      { ...generateMatch(), league: \"Champions League\" },\r\n      { ...generateMatch(), league: \"Europa League\" }\r\n    ];\r\n    description = \"🏆 **Bilet Competiții Europene** - Meciurile mari ale săptămânii!\";\r\n  } else {\r\n    // General ticket\r\n    matches = [\r\n      generateMatch(),\r\n      generateMatch(Math.random() > 0.5), // 50% chance for Romanian match\r\n      generateMatch()\r\n    ];\r\n    description = \"🎯 **Bilet personalizat** - Generat special pentru tine!\";\r\n  }\r\n\r\n  const totalOdds = matches.reduce((acc, match) => acc * parseFloat(match.odds), 1);\r\n\r\n  return {\r\n    id: Date.now().toString(),\r\n    type: \"chat\",\r\n    matches,\r\n    totalOdds: parseFloat(totalOdds.toFixed(2)),\r\n    description,\r\n    confidence\r\n  };\r\n}\r\n\r\n// Format ticket for display\r\nexport function formatTicket(ticket: Ticket): string {\r\n  let formatted = `${ticket.description}\\n\\n`;\r\n  \r\n  ticket.matches.forEach((match, index) => {\r\n    const predictionText = predictionTypes[match.prediction as keyof typeof predictionTypes] || match.prediction;\r\n    formatted += `${index + 1}. **${match.team1}** vs **${match.team2}**\\n`;\r\n    formatted += `   📍 ${match.league}`;\r\n    if (match.time) formatted += ` | ⏰ ${match.time}`;\r\n    formatted += `\\n   🎯 ${predictionText} | 💰 Cotă: ${match.odds}\\n\\n`;\r\n  });\r\n  \r\n  formatted += `💎 **Cotă totală: ${ticket.totalOdds}**\\n`;\r\n  formatted += `📊 **Încredere AI: ${ticket.confidence}%**`;\r\n  \r\n  return formatted;\r\n}\r\n"], "names": [], "mappings": "AAAA,gCAAgC;AAChC,6FAA6F;;;;;;AAoB7F,6BAA6B;AAC7B,MAAM,gBAAgB;IACpB;IAAQ;IAAY;IAAyB;IAC7C;IAAa;IAAY;IAAmB;IAC5C;IAAoB;IAAqB;IAAgB;CAC1D;AAED,sBAAsB;AACtB,MAAM,qBAAqB;IACzB;IAAe;IAAa;IAAmB;IAC/C;IAAiB;IAAO;IAAY;IAAe;IACnD;IAAW;IAAW;IAAa;IACnC;IAAmB;IAAqB;IACxC;IAAU;IAAQ;IAAS;IAAY;IAAY;CACpD;AAED,MAAM,UAAU;IACd;IAAkB;IAAkB;IAAW;IAC/C;IAAc;IAAW;IAAoB;CAC9C;AAED,8CAA8C;AAC9C,MAAM,kBAAkB;IACtB,KAAK;IACL,KAAK;IACL,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,YAAY;IACZ,aAAa;IACb,QAAQ;AACV;AAEA,gDAAgD;AAChD,SAAS,aAAa,UAAkB;IACtC,MAAM,aAAkD;QACtD,KAAK;YAAC;YAAM;SAAK;QACjB,KAAK;YAAC;YAAM;SAAK;QACjB,KAAK;YAAC;YAAM;SAAK;QACjB,MAAM;YAAC;YAAM;SAAK;QAClB,MAAM;YAAC;YAAM;SAAK;QAClB,MAAM;YAAC;YAAM;SAAK;QAClB,YAAY;YAAC;YAAM;SAAK;QACxB,aAAa;YAAC;YAAM;SAAK;QACzB,QAAQ;YAAC;YAAM;SAAK;IACtB;IAEA,MAAM,CAAC,KAAK,IAAI,GAAG,UAAU,CAAC,WAAW,IAAI;QAAC;QAAM;KAAK;IACzD,MAAM,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI;IAC3C,OAAO,KAAK,OAAO,CAAC;AACtB;AAEA,0BAA0B;AAC1B,SAAS,cAAc,mBAA4B,KAAK;IACtD,MAAM,QAAQ,mBAAmB,gBAAgB;IACjD,MAAM,SAAS,mBAAmB,mBAAmB,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,QAAQ,MAAM,GAAG,CAAC,KAAK,EAAE;IAElH,MAAM,QAAQ,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;IAC7D,IAAI,QAAQ,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;IAC3D,MAAO,UAAU,MAAO;QACtB,QAAQ,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;IACzD;IAEA,MAAM,cAAc,OAAO,IAAI,CAAC;IAChC,MAAM,aAAa,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,YAAY,MAAM,EAAE;IAC9E,MAAM,OAAO,aAAa;IAE1B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,MAAM,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;IACzG;AACF;AAGO,SAAS,yBAAyB,IAAY;IACnD,IAAI,UAAmB,EAAE;IACzB,IAAI,cAAc;IAClB,IAAI,aAAa;IAEjB,OAAQ;QACN,KAAK;YACH,wBAAwB;YACxB,UAAU;gBACR;gBACA;gBACA,cAAc,MAAM,yBAAyB;aAC9C;YACD,2BAA2B;YAC3B,UAAU,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC9B,GAAG,KAAK;oBACR,MAAM,CAAC,WAAW,MAAM,IAAI,IAAI,MAAM,CAAC,EAAE,OAAO,CAAC;gBACnD,CAAC;YACD,cAAc;YACd,aAAa;YACb;QAEF,KAAK;YACH,sBAAsB;YACtB,UAAU;gBACR;gBACA;gBACA;aACD;YACD,kCAAkC;YAClC,UAAU,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC9B,GAAG,KAAK;oBACR,YAAY,KAAK,MAAM,KAAK,MAAM,MAAM,MAAM,UAAU;oBACxD,MAAM,KAAK,GAAG,CAAC,MAAM,WAAW,MAAM,IAAI,IAAI,KAAK,OAAO,CAAC;gBAC7D,CAAC;YACD,cAAc;YACd,aAAa;YACb;QAEF,KAAK;YACH,gCAAgC;YAChC,UAAU;gBACR;gBACA,cAAc;gBACd;aACD;YACD,cAAc;YACd,aAAa;YACb;QAEF,KAAK;YACH,gCAAgC;YAChC,UAAU;gBACR;gBACA;gBACA;gBACA,cAAc,MAAM,yBAAyB;aAC9C;YACD,cAAc;YACd,aAAa;YACb;QAEF;YACE,UAAU;gBAAC;gBAAiB;gBAAiB;aAAgB;YAC7D,cAAc;YACd,aAAa;IACjB;IAEA,MAAM,YAAY,QAAQ,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,WAAW,MAAM,IAAI,GAAG;IAE/E,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB;QACA;QACA,WAAW,WAAW,UAAU,OAAO,CAAC;QACxC;QACA;IACF;AACF;AAGO,SAAS,mBAAmB,KAAa;IAC9C,MAAM,aAAa,MAAM,WAAW;IACpC,IAAI,UAAmB,EAAE;IACzB,IAAI,cAAc;IAClB,MAAM,aAAa;IAEnB,sCAAsC;IACtC,IAAI,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,SAAS;QAC/D,OAAO,yBAAyB;IAClC,OAAO,IAAI,WAAW,QAAQ,CAAC,cAAc,WAAW,QAAQ,CAAC,SAAS;QACxE,OAAO,yBAAyB;IAClC,OAAO,IAAI,WAAW,QAAQ,CAAC,YAAY,WAAW,QAAQ,CAAC,WAAW;QACxE,wBAAwB;QACxB,UAAU;YACR,cAAc;YACd,cAAc;YACd,cAAc;SACf;QACD,cAAc;IAChB,OAAO,IAAI,WAAW,QAAQ,CAAC,gBAAgB,WAAW,QAAQ,CAAC,WAAW;QAC5E,wBAAwB;QACxB,UAAU;YACR;gBAAE,GAAG,eAAe;gBAAE,QAAQ;YAAmB;YACjD;gBAAE,GAAG,eAAe;gBAAE,QAAQ;YAAmB;YACjD;gBAAE,GAAG,eAAe;gBAAE,QAAQ;YAAgB;SAC/C;QACD,cAAc;IAChB,OAAO;QACL,iBAAiB;QACjB,UAAU;YACR;YACA,cAAc,KAAK,MAAM,KAAK;YAC9B;SACD;QACD,cAAc;IAChB;IAEA,MAAM,YAAY,QAAQ,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,WAAW,MAAM,IAAI,GAAG;IAE/E,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,MAAM;QACN;QACA,WAAW,WAAW,UAAU,OAAO,CAAC;QACxC;QACA;IACF;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI,YAAY,GAAG,OAAO,WAAW,CAAC,IAAI,CAAC;IAE3C,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;QAC7B,MAAM,iBAAiB,eAAe,CAAC,MAAM,UAAU,CAAiC,IAAI,MAAM,UAAU;QAC5G,aAAa,GAAG,QAAQ,EAAE,IAAI,EAAE,MAAM,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,CAAC,IAAI,CAAC;QACvE,aAAa,CAAC,MAAM,EAAE,MAAM,MAAM,EAAE;QACpC,IAAI,MAAM,IAAI,EAAE,aAAa,CAAC,KAAK,EAAE,MAAM,IAAI,EAAE;QACjD,aAAa,CAAC,QAAQ,EAAE,eAAe,YAAY,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC;IACvE;IAEA,aAAa,CAAC,kBAAkB,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC;IACxD,aAAa,CAAC,mBAAmB,EAAE,OAAO,UAAU,CAAC,GAAG,CAAC;IAEzD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { generatePredefinedTicket, generateChatTicket, formatTicket, type Ticket } from '@/lib/ticketGenerator';\r\n\r\ninterface Message {\r\n  id: string;\r\n  text: string;\r\n  isUser: boolean;\r\n  ticketId?: string;\r\n  shareUrl?: string;\r\n}\r\n\r\nexport default function Home() {\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [inputValue, setInputValue] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  // Generate shareable URL for ticket\r\n  const generateShareUrl = async (ticket: Ticket): Promise<string> => {\r\n    try {\r\n      const response = await fetch('/api/tickets', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ ticket }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error('Failed to create shared ticket');\r\n      }\r\n\r\n      const data = await response.json();\r\n      return data.shareUrl;\r\n    } catch (error) {\r\n      console.error('Error creating share URL:', error);\r\n      // Fallback to a simple URL if API fails\r\n      const ticketId = `ticket_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n      return `${window.location.origin}/bilet/${ticketId}`;\r\n    }\r\n  };\r\n\r\n  // Copy share URL to clipboard\r\n  const copyToClipboard = async (text: string) => {\r\n    try {\r\n      await navigator.clipboard.writeText(text);\r\n      alert('Link copiat în clipboard!');\r\n    } catch (err) {\r\n      console.error('Failed to copy: ', err);\r\n      // Fallback for older browsers\r\n      const textArea = document.createElement('textarea');\r\n      textArea.value = text;\r\n      document.body.appendChild(textArea);\r\n      textArea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textArea);\r\n      alert('Link copiat în clipboard!');\r\n    }\r\n  };\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!inputValue.trim()) return;\r\n\r\n    const userMessage = {\r\n      id: Date.now().toString(),\r\n      text: inputValue,\r\n      isUser: true\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInputValue('');\r\n    setIsLoading(true);\r\n\r\n    // Simulate AI response\r\n    setTimeout(async () => {\r\n      const ticket = generateChatTicket(inputValue);\r\n      const shareUrl = await generateShareUrl(ticket);\r\n      const aiResponse = {\r\n        id: (Date.now() + 1).toString(),\r\n        text: formatTicket(ticket),\r\n        isUser: false,\r\n        ticketId: ticket.id,\r\n        shareUrl: shareUrl\r\n      };\r\n      setMessages(prev => [...prev, aiResponse]);\r\n      setIsLoading(false);\r\n    }, 1500);\r\n  };\r\n\r\n  const handlePredefinedButton = (type: string) => {\r\n    setIsLoading(true);\r\n\r\n    setTimeout(async () => {\r\n      const ticket = generatePredefinedTicket(type);\r\n      const shareUrl = await generateShareUrl(ticket);\r\n      const response = {\r\n        id: Date.now().toString(),\r\n        text: formatTicket(ticket),\r\n        isUser: false,\r\n        ticketId: ticket.id,\r\n        shareUrl: shareUrl\r\n      };\r\n      setMessages(prev => [...prev, response]);\r\n      setIsLoading(false);\r\n    }, 1000);\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-darker-surface to-dark-surface\">\r\n      {/* Header */}\r\n      <header className=\"border-b border-dark-surface bg-darker-surface/80 backdrop-blur-sm\">\r\n        <div className=\"container mx-auto px-4 py-4 sm:py-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h1 className=\"text-2xl sm:text-3xl font-bold gradient-text\">\r\n              AIpariat\r\n            </h1>\r\n            <div className=\"flex items-center space-x-2 text-xs sm:text-sm text-gray-400\">\r\n              <span className=\"w-2 h-2 bg-primary rounded-full animate-pulse\"></span>\r\n              <span className=\"hidden sm:inline\">AI Activ</span>\r\n              <span className=\"sm:hidden\">AI</span>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-gray-300 mt-2 text-sm sm:text-base\">\r\n            Toate casele de pariuri folosesc algoritmi pentru garantarea profitului. Am creat AIpariat pentru egalizarea șanselor.\r\n          </p>\r\n        </div>\r\n      </header>\r\n\r\n      <div className=\"container mx-auto px-4 py-4 sm:py-8 max-w-6xl\">\r\n        <div className=\"grid lg:grid-cols-3 gap-6 lg:gap-8\">\r\n          {/* Predefined Buttons */}\r\n          <div className=\"lg:col-span-1 order-2 lg:order-1\">\r\n            <h2 className=\"text-lg sm:text-xl font-semibold mb-4 sm:mb-6 text-primary\">\r\n              Bilete Rapide\r\n            </h2>\r\n            <div className=\"space-y-3 sm:space-y-4\">\r\n              <button\r\n                onClick={() => handlePredefinedButton('curajos')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-casino-red to-secondary rounded-lg border border-casino-red/30 hover:border-casino-red/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">🔥 Cel mai curajos bilet</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Cote mari, risc ridicat</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('sigur')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-casino-green to-primary rounded-lg border border-casino-green/30 hover:border-casino-green/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">🛡️ Cel mai sigur bilet</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Cote mici, șanse mari</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('ziua')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-ai-blue to-ai-purple rounded-lg border border-ai-blue/30 hover:border-ai-blue/60 transition-all duration-300 glow-ai hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">⭐ Recomandarea zilei</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Selecția AI pentru astăzi</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('saptamana')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-accent to-secondary rounded-lg border border-accent/30 hover:border-accent/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-black text-sm sm:text-base\">👑 Recomandarea săptămânii</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-800 mt-1\">Biletul premium al săptămânii</div>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Chat Interface */}\r\n          <div className=\"lg:col-span-2 order-1 lg:order-2\">\r\n            <h2 className=\"text-lg sm:text-xl font-semibold mb-4 sm:mb-6 text-primary\">\r\n              Chat cu AI-ul nostru\r\n            </h2>\r\n\r\n            {/* Chat Messages */}\r\n            <div className=\"bg-dark-surface rounded-lg border border-primary/20 h-64 sm:h-80 lg:h-96 overflow-y-auto p-3 sm:p-4 mb-4\">\r\n              {messages.length === 0 ? (\r\n                <div className=\"flex items-center justify-center h-full text-gray-400\">\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-4xl mb-4\">🤖</div>\r\n                    <p>Salut! Sunt AIpariat, expertul în pariuri sportive care folosește cele mai avansate analize AI.</p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-4\">\r\n                  {messages.map((message) => (\r\n                    <div\r\n                      key={message.id}\r\n                      className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}\r\n                    >\r\n                      <div\r\n                        className={`max-w-[280px] sm:max-w-xs lg:max-w-md px-3 sm:px-4 py-2 rounded-lg ${\r\n                          message.isUser\r\n                            ? 'bg-primary text-black'\r\n                            : 'bg-darker-surface text-white border border-primary/30'\r\n                        }`}\r\n                      >\r\n                        <div className=\"whitespace-pre-line text-sm\">\r\n                          {message.text}\r\n                        </div>\r\n                        {!message.isUser && message.shareUrl && (\r\n                          <div className=\"mt-3 pt-3 border-t border-primary/20\">\r\n                            <button\r\n                              onClick={() => copyToClipboard(message.shareUrl!)}\r\n                              className=\"flex items-center space-x-2 text-xs bg-primary/20 hover:bg-primary/30 px-3 py-1 rounded transition-colors\"\r\n                            >\r\n                              <span>🔗</span>\r\n                              <span>Partajează biletul</span>\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                  {isLoading && (\r\n                    <div className=\"flex justify-start\">\r\n                      <div className=\"bg-darker-surface text-white border border-primary/30 px-4 py-2 rounded-lg\">\r\n                        <div className=\"flex items-center space-x-2\">\r\n                          <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce\"></div>\r\n                          <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\r\n                          <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Chat Input */}\r\n            <div className=\"flex flex-col sm:flex-row gap-2 sm:gap-2\">\r\n              <input\r\n                type=\"text\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}\r\n                placeholder=\"Descrie biletul dorit\"\r\n                disabled={isLoading}\r\n                className=\"flex-1 px-3 sm:px-4 py-3 bg-darker-surface border border-primary/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary disabled:opacity-50 text-sm sm:text-base\"\r\n              />\r\n              <button\r\n                onClick={handleSendMessage}\r\n                disabled={isLoading || !inputValue.trim()}\r\n                className=\"px-4 sm:px-6 py-3 bg-primary text-black font-semibold rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base whitespace-nowrap\"\r\n              >\r\n                Trimite\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <footer className=\"mt-8 sm:mt-16 text-center text-gray-400 text-xs sm:text-sm\">\r\n          <div className=\"border-t border-dark-surface pt-6 sm:pt-8\">\r\n            <p>&copy; 2025 AIpariat. Toate drepturile rezervate.</p>\r\n            <p className=\"mt-2\">\r\n              Această platformă are exclusiv rol consultativ și nu poate fi folosită pentru plasarea pariurilor sportive.\r\n            </p>\r\n          </div>\r\n        </footer>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAae,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,oCAAoC;IACpC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,QAAQ;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wCAAwC;YACxC,MAAM,WAAW,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAClF,OAAO,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,UAAU;QACtD;IACF;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oBAAoB;YAClC,8BAA8B;YAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,MAAM;YACf,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,cAAc;YAClB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,QAAQ;QACV;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QACd,aAAa;QAEb,uBAAuB;QACvB,WAAW;YACT,MAAM,SAAS,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE;YAClC,MAAM,WAAW,MAAM,iBAAiB;YACxC,MAAM,aAAa;gBACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,QAAQ;gBACR,UAAU,OAAO,EAAE;gBACnB,UAAU;YACZ;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YACzC,aAAa;QACf,GAAG;IACL;IAEA,MAAM,yBAAyB,CAAC;QAC9B,aAAa;QAEb,WAAW;YACT,MAAM,SAAS,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD,EAAE;YACxC,MAAM,WAAW,MAAM,iBAAiB;YACxC,MAAM,WAAW;gBACf,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,QAAQ;gBACR,UAAU,OAAO,EAAE;gBACnB,UAAU;YACZ;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAS;YACvC,aAAa;QACf,GAAG;IACL;IAIA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAG7D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,6LAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;;;;;;;sCAGhC,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAM3D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAG3E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,6LAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,6LAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,6LAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAK3E,6LAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;kEAAE;;;;;;;;;;;;;;;;iEAIP,6LAAC;4CAAI,WAAU;;gDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wDAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,gBAAgB,iBAAiB;kEAErE,cAAA,6LAAC;4DACC,WAAW,CAAC,mEAAmE,EAC7E,QAAQ,MAAM,GACV,0BACA,yDACJ;;8EAEF,6LAAC;oEAAI,WAAU;8EACZ,QAAQ,IAAI;;;;;;gEAEd,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,kBAClC,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEACC,SAAS,IAAM,gBAAgB,QAAQ,QAAQ;wEAC/C,WAAU;;0FAEV,6LAAC;0FAAK;;;;;;0FACN,6LAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;uDApBT,QAAQ,EAAE;;;;;gDA2BlB,2BACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;oEAAiD,OAAO;wEAAC,gBAAgB;oEAAM;;;;;;8EAC9F,6LAAC;oEAAI,WAAU;oEAAiD,OAAO;wEAAC,gBAAgB;oEAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAU5G,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;gDACxC,aAAY;gDACZ,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC;gDACC,SAAS;gDACT,UAAU,aAAa,CAAC,WAAW,IAAI;gDACvC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAE,WAAU;8CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;GA1QwB;KAAA", "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}