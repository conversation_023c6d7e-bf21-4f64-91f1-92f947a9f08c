{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/src/lib/ticketGenerator.ts"], "sourcesContent": ["// Ticket Generator for AIpariat\r\n// Integrates with real AI API for betting slip generation\r\n\r\nexport interface Match {\r\n  match: string;\r\n  date: string;\r\n  sport: string;\r\n  bet_type: string;\r\n  selection: string;\r\n  odds: number;\r\n  league: string;\r\n}\r\n\r\nexport interface Ticket {\r\n  id: string;\r\n  type: string;\r\n  matches: Match[];\r\n  totalOdds: number;\r\n  description: string;\r\n  confidence: string;\r\n  explanation?: string;\r\n  recommended_stake?: number;\r\n  potential_win?: number;\r\n  risk_level?: string;\r\n}\r\n\r\n// API Configuration\r\nconst API_BASE_URL = 'https://aipariatagent-1914c9cd53a9.herokuapp.com';\r\n\r\n// API Response interfaces\r\ninterface ApiTaskResponse {\r\n  task_id: string;\r\n  status: string;\r\n  message: string;\r\n}\r\n\r\ninterface ApiStatusResponse {\r\n  task_id: string;\r\n  status: 'pending' | 'running' | 'completed' | 'failed';\r\n  progress?: number;\r\n  created_at?: string;\r\n  completed_at?: string;\r\n}\r\n\r\ninterface ApiBettingSlip {\r\n  matches: {\r\n    match: string;\r\n    date: string;\r\n    sport: string;\r\n    bet_type: string;\r\n    selection: string;\r\n    odds: number;\r\n    league: string;\r\n  }[];\r\n  total_odds: number;\r\n  recommended_stake?: number;\r\n  potential_win?: number;\r\n  risk_level?: string;\r\n  confidence: string;\r\n  explanation?: string;\r\n}\r\n\r\ninterface ApiResultResponse {\r\n  task_id: string;\r\n  status: string;\r\n  query: string;\r\n  betting_slip: ApiBettingSlip;\r\n  grounding_info?: {\r\n    has_grounding: boolean;\r\n    web_search_queries: string[];\r\n    sources: { title: string; uri: string }[];\r\n  };\r\n}\r\n\r\n// API Integration Functions\r\nasync function createBettingSlipTask(query: string, options: {\r\n  max_odds?: number;\r\n  budget?: number;\r\n  sport?: string;\r\n} = {}): Promise<string> {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/betting-slip/create`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        query,\r\n        ...options\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`API request failed: ${response.status}`);\r\n    }\r\n\r\n    const data: ApiTaskResponse = await response.json();\r\n    return data.task_id;\r\n  } catch (error) {\r\n    console.error('Error creating betting slip task:', error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nasync function pollTaskStatus(taskId: string): Promise<ApiResultResponse> {\r\n  const maxAttempts = 30; // 30 attempts * 2 seconds = 60 seconds max\r\n  let attempts = 0;\r\n\r\n  while (attempts < maxAttempts) {\r\n    try {\r\n      const response = await fetch(`${API_BASE_URL}/betting-slip/status/${taskId}`);\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Status check failed: ${response.status}`);\r\n      }\r\n\r\n      const statusData: ApiStatusResponse = await response.json();\r\n\r\n      if (statusData.status === 'completed') {\r\n        // Add a small delay to ensure result is ready\r\n        await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n        // Try to get the result with retry logic\r\n        let resultAttempts = 0;\r\n        const maxResultAttempts = 3;\r\n\r\n        while (resultAttempts < maxResultAttempts) {\r\n          try {\r\n            const resultResponse = await fetch(`${API_BASE_URL}/betting-slip/result/${taskId}`);\r\n\r\n            if (resultResponse.ok) {\r\n              return await resultResponse.json();\r\n            } else if (resultResponse.status === 404) {\r\n              console.warn(`Result not ready yet, attempt ${resultAttempts + 1}/${maxResultAttempts}`);\r\n              if (resultAttempts < maxResultAttempts - 1) {\r\n                await new Promise(resolve => setTimeout(resolve, 2000));\r\n                resultAttempts++;\r\n                continue;\r\n              }\r\n            }\r\n\r\n            throw new Error(`Result fetch failed: ${resultResponse.status}`);\r\n          } catch (resultError) {\r\n            if (resultAttempts >= maxResultAttempts - 1) {\r\n              throw resultError;\r\n            }\r\n            await new Promise(resolve => setTimeout(resolve, 2000));\r\n            resultAttempts++;\r\n          }\r\n        }\r\n      } else if (statusData.status === 'failed') {\r\n        throw new Error('Betting slip generation failed');\r\n      }\r\n\r\n      // Wait 2 seconds before next poll\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n      attempts++;\r\n    } catch (error) {\r\n      console.error('Error polling task status:', error);\r\n      if (attempts >= maxAttempts - 1) {\r\n        throw error;\r\n      }\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n      attempts++;\r\n    }\r\n  }\r\n\r\n  throw new Error('Task timeout - betting slip generation took too long');\r\n}\r\n\r\nfunction convertApiResponseToTicket(apiResponse: ApiResultResponse, type: string): Ticket {\r\n  const bettingSlip = apiResponse.betting_slip;\r\n\r\n  return {\r\n    id: Date.now().toString(),\r\n    type,\r\n    matches: bettingSlip.matches,\r\n    totalOdds: bettingSlip.total_odds,\r\n    description: getDescriptionForType(type),\r\n    confidence: bettingSlip.confidence,\r\n    explanation: bettingSlip.explanation,\r\n    recommended_stake: bettingSlip.recommended_stake,\r\n    potential_win: bettingSlip.potential_win,\r\n    risk_level: bettingSlip.risk_level\r\n  };\r\n}\r\n\r\nfunction getDescriptionForType(type: string): string {\r\n  switch (type) {\r\n    case \"curajos\":\r\n      return \"🔥 **Cel mai curajos bilet** - Cote mari pentru câștiguri spectaculoase!\";\r\n    case \"sigur\":\r\n      return \"🛡️ **Cel mai sigur bilet** - Selecții cu șanse mari de câștig!\";\r\n    case \"ziua\":\r\n      return \"⭐ **Recomandarea zilei** - Selecția AI pentru astăzi!\";\r\n    case \"saptamana\":\r\n      return \"👑 **Recomandarea săptămânii** - Biletul premium al săptămânii!\";\r\n    default:\r\n      return \"🎯 **Bilet personalizat** - Generat special pentru tine!\";\r\n  }\r\n}\r\n\r\nfunction getQueryForType(type: string): string {\r\n  switch (type) {\r\n    case \"curajos\":\r\n      return \"Vreau un bilet curajos cu cote mari și risc ridicat pentru meciuri din următoarele zile\";\r\n    case \"sigur\":\r\n      return \"Vreau un bilet sigur cu cote mici și șanse mari de câștig pentru meciuri din următoarele zile\";\r\n    case \"ziua\":\r\n      return \"Vreau recomandarea zilei pentru pariuri sportive cu analiză AI pentru astăzi\";\r\n    case \"saptamana\":\r\n      return \"Vreau recomandarea săptămânii pentru pariuri sportive, biletul premium al săptămânii\";\r\n    default:\r\n      return \"Vreau un bilet pentru pariuri sportive cu analiză AI\";\r\n  }\r\n}\r\n\r\n// Generate predefined tickets using real API\r\nexport async function generatePredefinedTicket(type: string): Promise<Ticket> {\r\n  try {\r\n    const query = getQueryForType(type);\r\n    const options: { max_odds?: number; budget?: number; sport?: string } = {};\r\n\r\n    // Set specific options based on type\r\n    switch (type) {\r\n      case \"curajos\":\r\n        options.max_odds = 10.0; // Higher odds for brave bets\r\n        break;\r\n      case \"sigur\":\r\n        options.max_odds = 3.0; // Lower odds for safe bets\r\n        break;\r\n      case \"ziua\":\r\n        options.sport = \"fotbal\";\r\n        break;\r\n      case \"saptamana\":\r\n        options.budget = 100.0;\r\n        break;\r\n    }\r\n\r\n    console.log(`🎯 Generating ${type} ticket with query: \"${query}\" and options:`, options);\r\n\r\n    const taskId = await createBettingSlipTask(query, options);\r\n    console.log(`✅ Task created with ID: ${taskId}`);\r\n\r\n    const apiResponse = await pollTaskStatus(taskId);\r\n    console.log(`✅ API response received for ${type} ticket`);\r\n\r\n    return convertApiResponseToTicket(apiResponse, type);\r\n  } catch (error) {\r\n    console.error(`❌ Error generating ${type} ticket:`, error);\r\n    // Return fallback ticket in case of API failure\r\n    return createFallbackTicket(type, undefined, error.message);\r\n  }\r\n}\r\n\r\n// Generate ticket based on user query using real API\r\nexport async function generateChatTicket(query: string): Promise<Ticket> {\r\n  try {\r\n    console.log(`💬 Generating chat ticket with query: \"${query}\"`);\r\n\r\n    const taskId = await createBettingSlipTask(query);\r\n    console.log(`✅ Chat task created with ID: ${taskId}`);\r\n\r\n    const apiResponse = await pollTaskStatus(taskId);\r\n    console.log(`✅ Chat API response received`);\r\n\r\n    return convertApiResponseToTicket(apiResponse, \"chat\");\r\n  } catch (error) {\r\n    console.error('❌ Error generating chat ticket:', error);\r\n    // Return fallback ticket in case of API failure\r\n    return createFallbackTicket(\"chat\", query, error.message);\r\n  }\r\n}\r\n\r\n// Fallback function for when API fails\r\nfunction createFallbackTicket(type: string, query?: string, errorMessage?: string): Ticket {\r\n  const fallbackMatches: Match[] = [\r\n    {\r\n      match: \"FCSB vs CFR Cluj\",\r\n      date: \"2025-07-10 20:00\",\r\n      sport: \"fotbal\",\r\n      bet_type: \"1X2\",\r\n      selection: \"1\",\r\n      odds: 2.10,\r\n      league: \"Liga 1 Romania\"\r\n    },\r\n    {\r\n      match: \"Real Madrid vs Barcelona\",\r\n      date: \"2025-07-11 21:00\",\r\n      sport: \"fotbal\",\r\n      bet_type: \"1X2\",\r\n      selection: \"X\",\r\n      odds: 3.20,\r\n      league: \"La Liga\"\r\n    },\r\n    {\r\n      match: \"Manchester City vs Liverpool\",\r\n      date: \"2025-07-12 17:30\",\r\n      sport: \"fotbal\",\r\n      bet_type: \"Over/Under\",\r\n      selection: \"Over 2.5\",\r\n      odds: 1.85,\r\n      league: \"Premier League\"\r\n    }\r\n  ];\r\n\r\n  const totalOdds = fallbackMatches.reduce((acc, match) => acc * match.odds, 1);\r\n\r\n  const explanation = errorMessage\r\n    ? `Bilet generat cu date simulate. Eroare API: ${errorMessage}. Încearcă din nou în câteva momente.`\r\n    : \"Bilet generat cu date simulate din cauza unei probleme temporare cu API-ul. Încearcă din nou în câteva momente.\";\r\n\r\n  return {\r\n    id: Date.now().toString(),\r\n    type,\r\n    matches: fallbackMatches,\r\n    totalOdds: parseFloat(totalOdds.toFixed(2)),\r\n    description: query ? \"🎯 **Bilet personalizat** - Generat special pentru tine!\" : getDescriptionForType(type),\r\n    confidence: \"mediu\",\r\n    explanation,\r\n    risk_level: \"mediu\"\r\n  };\r\n}\r\n\r\n// Format ticket for display\r\nexport function formatTicket(ticket: Ticket): string {\r\n  let formatted = `${ticket.description}\\n\\n`;\r\n\r\n  ticket.matches.forEach((match, index) => {\r\n    // Parse match string to get team names\r\n    const teams = match.match.split(' vs ');\r\n    const team1 = teams[0] || match.match;\r\n    const team2 = teams[1] || '';\r\n\r\n    formatted += `${index + 1}. **${team1}**${team2 ? ` vs **${team2}**` : ''}\\n`;\r\n    formatted += `   📍 ${match.league}`;\r\n    if (match.date) {\r\n      const date = new Date(match.date);\r\n      const timeStr = date.toLocaleTimeString('ro-RO', { hour: '2-digit', minute: '2-digit' });\r\n      formatted += ` | ⏰ ${timeStr}`;\r\n    }\r\n    formatted += `\\n   🎯 ${match.bet_type}: ${match.selection} | 💰 Cotă: ${match.odds}\\n\\n`;\r\n  });\r\n\r\n  formatted += `💎 **Cotă totală: ${ticket.totalOdds}**\\n`;\r\n  formatted += `📊 **Încredere AI: ${ticket.confidence}**`;\r\n\r\n  if (ticket.explanation) {\r\n    formatted += `\\n\\n📝 **Explicație:**\\n${ticket.explanation}`;\r\n  }\r\n\r\n  if (ticket.recommended_stake) {\r\n    formatted += `\\n\\n💰 **Miză recomandată: ${ticket.recommended_stake} RON**`;\r\n  }\r\n\r\n  if (ticket.potential_win) {\r\n    formatted += `\\n🎯 **Câștig potențial: ${ticket.potential_win} RON**`;\r\n  }\r\n\r\n  if (ticket.risk_level) {\r\n    const riskEmoji = ticket.risk_level === 'ridicat' ? '🔥' : ticket.risk_level === 'mediu' ? '⚖️' : '🛡️';\r\n    formatted += `\\n${riskEmoji} **Nivel risc: ${ticket.risk_level}**`;\r\n  }\r\n\r\n  return formatted;\r\n}\r\n"], "names": [], "mappings": "AAAA,gCAAgC;AAChC,0DAA0D;;;;;;AAyB1D,oBAAoB;AACpB,MAAM,eAAe;AA+CrB,4BAA4B;AAC5B,eAAe,sBAAsB,KAAa,EAAE,UAIhD,CAAC,CAAC;IACJ,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,oBAAoB,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA,GAAG,OAAO;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;QAC1D;QAEA,MAAM,OAAwB,MAAM,SAAS,IAAI;QACjD,OAAO,KAAK,OAAO;IACrB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM;IACR;AACF;AAEA,eAAe,eAAe,MAAc;IAC1C,MAAM,cAAc,IAAI,2CAA2C;IACnE,IAAI,WAAW;IAEf,MAAO,WAAW,YAAa;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,qBAAqB,EAAE,QAAQ;YAE5E,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,MAAM,EAAE;YAC3D;YAEA,MAAM,aAAgC,MAAM,SAAS,IAAI;YAEzD,IAAI,WAAW,MAAM,KAAK,aAAa;gBACrC,8CAA8C;gBAC9C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,yCAAyC;gBACzC,IAAI,iBAAiB;gBACrB,MAAM,oBAAoB;gBAE1B,MAAO,iBAAiB,kBAAmB;oBACzC,IAAI;wBACF,MAAM,iBAAiB,MAAM,MAAM,GAAG,aAAa,qBAAqB,EAAE,QAAQ;wBAElF,IAAI,eAAe,EAAE,EAAE;4BACrB,OAAO,MAAM,eAAe,IAAI;wBAClC,OAAO,IAAI,eAAe,MAAM,KAAK,KAAK;4BACxC,QAAQ,IAAI,CAAC,CAAC,8BAA8B,EAAE,iBAAiB,EAAE,CAAC,EAAE,mBAAmB;4BACvF,IAAI,iBAAiB,oBAAoB,GAAG;gCAC1C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gCACjD;gCACA;4BACF;wBACF;wBAEA,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,eAAe,MAAM,EAAE;oBACjE,EAAE,OAAO,aAAa;wBACpB,IAAI,kBAAkB,oBAAoB,GAAG;4BAC3C,MAAM;wBACR;wBACA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;wBACjD;oBACF;gBACF;YACF,OAAO,IAAI,WAAW,MAAM,KAAK,UAAU;gBACzC,MAAM,IAAI,MAAM;YAClB;YAEA,kCAAkC;YAClC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,IAAI,YAAY,cAAc,GAAG;gBAC/B,MAAM;YACR;YACA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD;QACF;IACF;IAEA,MAAM,IAAI,MAAM;AAClB;AAEA,SAAS,2BAA2B,WAA8B,EAAE,IAAY;IAC9E,MAAM,cAAc,YAAY,YAAY;IAE5C,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB;QACA,SAAS,YAAY,OAAO;QAC5B,WAAW,YAAY,UAAU;QACjC,aAAa,sBAAsB;QACnC,YAAY,YAAY,UAAU;QAClC,aAAa,YAAY,WAAW;QACpC,mBAAmB,YAAY,iBAAiB;QAChD,eAAe,YAAY,aAAa;QACxC,YAAY,YAAY,UAAU;IACpC;AACF;AAEA,SAAS,sBAAsB,IAAY;IACzC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,SAAS,gBAAgB,IAAY;IACnC,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAGO,eAAe,yBAAyB,IAAY;IACzD,IAAI;QACF,MAAM,QAAQ,gBAAgB;QAC9B,MAAM,UAAkE,CAAC;QAEzE,qCAAqC;QACrC,OAAQ;YACN,KAAK;gBACH,QAAQ,QAAQ,GAAG,MAAM,6BAA6B;gBACtD;YACF,KAAK;gBACH,QAAQ,QAAQ,GAAG,KAAK,2BAA2B;gBACnD;YACF,KAAK;gBACH,QAAQ,KAAK,GAAG;gBAChB;YACF,KAAK;gBACH,QAAQ,MAAM,GAAG;gBACjB;QACJ;QAEA,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK,qBAAqB,EAAE,MAAM,cAAc,CAAC,EAAE;QAEhF,MAAM,SAAS,MAAM,sBAAsB,OAAO;QAClD,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QAE/C,MAAM,cAAc,MAAM,eAAe;QACzC,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,CAAC;QAExD,OAAO,2BAA2B,aAAa;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,EAAE;QACpD,gDAAgD;QAChD,OAAO,qBAAqB,MAAM,WAAW,MAAM,OAAO;IAC5D;AACF;AAGO,eAAe,mBAAmB,KAAa;IACpD,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,MAAM,CAAC,CAAC;QAE9D,MAAM,SAAS,MAAM,sBAAsB;QAC3C,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,QAAQ;QAEpD,MAAM,cAAc,MAAM,eAAe;QACzC,QAAQ,GAAG,CAAC,CAAC,4BAA4B,CAAC;QAE1C,OAAO,2BAA2B,aAAa;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,gDAAgD;QAChD,OAAO,qBAAqB,QAAQ,OAAO,MAAM,OAAO;IAC1D;AACF;AAEA,uCAAuC;AACvC,SAAS,qBAAqB,IAAY,EAAE,KAAc,EAAE,YAAqB;IAC/E,MAAM,kBAA2B;QAC/B;YACE,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,MAAM;YACN,QAAQ;QACV;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,MAAM;YACN,QAAQ;QACV;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,MAAM;YACN,QAAQ;QACV;KACD;IAED,MAAM,YAAY,gBAAgB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,IAAI,EAAE;IAE3E,MAAM,cAAc,eAChB,CAAC,4CAA4C,EAAE,aAAa,qCAAqC,CAAC,GAClG;IAEJ,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB;QACA,SAAS;QACT,WAAW,WAAW,UAAU,OAAO,CAAC;QACxC,aAAa,QAAQ,6DAA6D,sBAAsB;QACxG,YAAY;QACZ;QACA,YAAY;IACd;AACF;AAGO,SAAS,aAAa,MAAc;IACzC,IAAI,YAAY,GAAG,OAAO,WAAW,CAAC,IAAI,CAAC;IAE3C,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;QAC7B,uCAAuC;QACvC,MAAM,QAAQ,MAAM,KAAK,CAAC,KAAK,CAAC;QAChC,MAAM,QAAQ,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK;QACrC,MAAM,QAAQ,KAAK,CAAC,EAAE,IAAI;QAE1B,aAAa,GAAG,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;QAC7E,aAAa,CAAC,MAAM,EAAE,MAAM,MAAM,EAAE;QACpC,IAAI,MAAM,IAAI,EAAE;YACd,MAAM,OAAO,IAAI,KAAK,MAAM,IAAI;YAChC,MAAM,UAAU,KAAK,kBAAkB,CAAC,SAAS;gBAAE,MAAM;gBAAW,QAAQ;YAAU;YACtF,aAAa,CAAC,KAAK,EAAE,SAAS;QAChC;QACA,aAAa,CAAC,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,MAAM,SAAS,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC;IAC3F;IAEA,aAAa,CAAC,kBAAkB,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC;IACxD,aAAa,CAAC,mBAAmB,EAAE,OAAO,UAAU,CAAC,EAAE,CAAC;IAExD,IAAI,OAAO,WAAW,EAAE;QACtB,aAAa,CAAC,wBAAwB,EAAE,OAAO,WAAW,EAAE;IAC9D;IAEA,IAAI,OAAO,iBAAiB,EAAE;QAC5B,aAAa,CAAC,2BAA2B,EAAE,OAAO,iBAAiB,CAAC,MAAM,CAAC;IAC7E;IAEA,IAAI,OAAO,aAAa,EAAE;QACxB,aAAa,CAAC,yBAAyB,EAAE,OAAO,aAAa,CAAC,MAAM,CAAC;IACvE;IAEA,IAAI,OAAO,UAAU,EAAE;QACrB,MAAM,YAAY,OAAO,UAAU,KAAK,YAAY,OAAO,OAAO,UAAU,KAAK,UAAU,OAAO;QAClG,aAAa,CAAC,EAAE,EAAE,UAAU,eAAe,EAAE,OAAO,UAAU,CAAC,EAAE,CAAC;IACpE;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport { generatePredefinedTicket, generateChatTicket, formatTicket, type Ticket } from '@/lib/ticketGenerator';\r\n\r\ninterface Message {\r\n  id: string;\r\n  text: string;\r\n  isUser: boolean;\r\n  ticketId?: string;\r\n  shareUrl?: string;\r\n}\r\n\r\nexport default function Home() {\r\n  const [messages, setMessages] = useState<Message[]>([]);\r\n  const [inputValue, setInputValue] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [loadingTime, setLoadingTime] = useState(0);\r\n  const [loadingMessage, setLoadingMessage] = useState('');\r\n  const [isFullscreen, setIsFullscreen] = useState(false);\r\n  const timerRef = useRef<NodeJS.Timeout | null>(null);\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Timer helper functions\r\n  const startTimer = (initialMessage: string) => {\r\n    setLoadingTime(0);\r\n    setLoadingMessage(initialMessage);\r\n\r\n    timerRef.current = setInterval(() => {\r\n      setLoadingTime(prev => {\r\n        const newTime = prev + 1;\r\n\r\n        // Update loading message based on time elapsed\r\n        if (newTime <= 5) {\r\n          setLoadingMessage('🤖 AI-ul analizează cererea ta...');\r\n        } else if (newTime <= 10) {\r\n          setLoadingMessage('📊 Colectez date despre meciuri...');\r\n        } else if (newTime <= 15) {\r\n          setLoadingMessage('⚽ Analizez statistici și cote...');\r\n        } else if (newTime <= 20) {\r\n          setLoadingMessage('🎯 Calculez predicții optimale...');\r\n        } else if (newTime <= 25) {\r\n          setLoadingMessage('💎 Finalizez biletul tău...');\r\n        } else {\r\n          setLoadingMessage('⏳ Încă lucrez la biletul perfect...');\r\n        }\r\n\r\n        return newTime;\r\n      });\r\n    }, 1000);\r\n  };\r\n\r\n  const stopTimer = () => {\r\n    if (timerRef.current) {\r\n      clearInterval(timerRef.current);\r\n      timerRef.current = null;\r\n    }\r\n    setLoadingTime(0);\r\n    setLoadingMessage('');\r\n  };\r\n\r\n  // Auto-scroll to bottom when messages change\r\n  const scrollToBottom = () => {\r\n    // Add a small delay to ensure DOM is updated, especially in fullscreen mode\r\n    setTimeout(() => {\r\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n    }, 100);\r\n  };\r\n\r\n  // Enhanced scroll effect that works better with fullscreen mode\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages, isLoading, isFullscreen]);\r\n\r\n  // Mobile fullscreen helpers\r\n  const enterFullscreen = () => {\r\n    // Check if we're on mobile (width <= 768px) or if we're in a mobile viewport\r\n    const isMobile = window.innerWidth <= 768 ||\r\n                     /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\r\n\r\n    if (isMobile) {\r\n      console.log('🔄 Entering fullscreen mode for mobile');\r\n      setIsFullscreen(true);\r\n      document.body.style.overflow = 'hidden';\r\n\r\n      // Force scroll to chat area after a brief delay to ensure DOM is updated\r\n      setTimeout(() => {\r\n        chatContainerRef.current?.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'start'\r\n        });\r\n      }, 100);\r\n    }\r\n  };\r\n\r\n  const exitFullscreen = () => {\r\n    console.log('🔄 Exiting fullscreen mode');\r\n    setIsFullscreen(false);\r\n    document.body.style.overflow = 'auto';\r\n  };\r\n\r\n  // Cleanup timer on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (timerRef.current) {\r\n        clearInterval(timerRef.current);\r\n      }\r\n      // Cleanup fullscreen on unmount\r\n      document.body.style.overflow = 'auto';\r\n    };\r\n  }, []);\r\n\r\n  // Generate shareable URL for ticket\r\n  const generateShareUrl = async (ticket: Ticket): Promise<string> => {\r\n    try {\r\n      const response = await fetch('/api/tickets', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ ticket }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Failed to create shared ticket: ${response.status}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      // Store ticket in localStorage as backup\r\n      const shareId = data.shareId;\r\n      if (shareId) {\r\n        localStorage.setItem(`ticket_${shareId}`, JSON.stringify({\r\n          ticket,\r\n          createdAt: new Date().toISOString(),\r\n          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days\r\n        }));\r\n      }\r\n\r\n      return data.shareUrl;\r\n    } catch (error) {\r\n      console.error('Error creating share URL:', error);\r\n\r\n      // Fallback: create local-only shareable ticket\r\n      const ticketId = `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n      localStorage.setItem(`ticket_${ticketId}`, JSON.stringify({\r\n        ticket,\r\n        createdAt: new Date().toISOString(),\r\n        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),\r\n        localOnly: true\r\n      }));\r\n\r\n      return `${window.location.origin}/bilet/${ticketId}`;\r\n    }\r\n  };\r\n\r\n  // Copy share URL to clipboard\r\n  const copyToClipboard = async (text: string) => {\r\n    try {\r\n      await navigator.clipboard.writeText(text);\r\n      alert('Link copiat în clipboard!');\r\n    } catch (err) {\r\n      console.error('Failed to copy: ', err);\r\n      // Fallback for older browsers\r\n      const textArea = document.createElement('textarea');\r\n      textArea.value = text;\r\n      document.body.appendChild(textArea);\r\n      textArea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textArea);\r\n      alert('Link copiat în clipboard!');\r\n    }\r\n  };\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!inputValue.trim()) return;\r\n\r\n    // Enter fullscreen on mobile\r\n    enterFullscreen();\r\n\r\n    const userMessage = {\r\n      id: Date.now().toString(),\r\n      text: inputValue,\r\n      isUser: true\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    const queryText = inputValue;\r\n    setInputValue('');\r\n    setIsLoading(true);\r\n    startTimer('🤖 AI-ul analizează cererea ta...');\r\n\r\n    // Ensure scroll to timer in fullscreen mode\r\n    if (isFullscreen) {\r\n      setTimeout(() => {\r\n        scrollToBottom();\r\n      }, 200);\r\n    }\r\n\r\n    try {\r\n      // Generate ticket using real API\r\n      const ticket = await generateChatTicket(queryText);\r\n      const shareUrl = await generateShareUrl(ticket);\r\n      const aiResponse = {\r\n        id: (Date.now() + 1).toString(),\r\n        text: formatTicket(ticket),\r\n        isUser: false,\r\n        ticketId: ticket.id,\r\n        shareUrl: shareUrl\r\n      };\r\n      setMessages(prev => [...prev, aiResponse]);\r\n    } catch (error) {\r\n      console.error('Error generating ticket:', error);\r\n      const errorResponse = {\r\n        id: (Date.now() + 1).toString(),\r\n        text: \"❌ Ne pare rău, a apărut o problemă la generarea biletului. Te rugăm să încerci din nou.\",\r\n        isUser: false\r\n      };\r\n      setMessages(prev => [...prev, errorResponse]);\r\n    } finally {\r\n      stopTimer();\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePredefinedButton = async (type: string) => {\r\n    // Enter fullscreen on mobile\r\n    enterFullscreen();\r\n\r\n    // Add a system message to show which button was pressed\r\n    const getButtonDescription = (buttonType: string) => {\r\n      switch (buttonType) {\r\n        case 'curajos':\r\n          return '🔥 Cel mai curajos bilet';\r\n        case 'sigur':\r\n          return '🛡️ Cel mai sigur bilet';\r\n        case 'ziua':\r\n          return '⭐ Recomandarea zilei';\r\n        case 'saptamana':\r\n          return '👑 Recomandarea săptămânii';\r\n        default:\r\n          return '🎯 Bilet personalizat';\r\n      }\r\n    };\r\n\r\n    // Add system message to chat\r\n    const systemMessage = {\r\n      id: Date.now().toString(),\r\n      text: `Ai selectat: ${getButtonDescription(type)}`,\r\n      isUser: true\r\n    };\r\n    setMessages(prev => [...prev, systemMessage]);\r\n\r\n    setIsLoading(true);\r\n\r\n    // Set specific timer message based on button type\r\n    const getTimerMessage = (buttonType: string) => {\r\n      switch (buttonType) {\r\n        case 'curajos':\r\n          return '🔥 Caut cele mai curajoase pariuri...';\r\n        case 'sigur':\r\n          return '🛡️ Analizez pariurile sigure...';\r\n        case 'ziua':\r\n          return '⭐ Pregătesc recomandarea zilei...';\r\n        case 'saptamana':\r\n          return '👑 Creez biletul premium al săptămânii...';\r\n        default:\r\n          return '🤖 AI-ul lucrează la biletul tău...';\r\n      }\r\n    };\r\n\r\n    startTimer(getTimerMessage(type));\r\n\r\n    // Ensure scroll to timer in fullscreen mode\r\n    if (isFullscreen) {\r\n      setTimeout(() => {\r\n        scrollToBottom();\r\n      }, 200);\r\n    }\r\n\r\n    try {\r\n      // Generate ticket using real API\r\n      const ticket = await generatePredefinedTicket(type);\r\n      const shareUrl = await generateShareUrl(ticket);\r\n      const response = {\r\n        id: Date.now().toString(),\r\n        text: formatTicket(ticket),\r\n        isUser: false,\r\n        ticketId: ticket.id,\r\n        shareUrl: shareUrl\r\n      };\r\n      setMessages(prev => [...prev, response]);\r\n    } catch (error) {\r\n      console.error('Error generating predefined ticket:', error);\r\n      const errorResponse = {\r\n        id: Date.now().toString(),\r\n        text: \"❌ Ne pare rău, a apărut o problemă la generarea biletului. Te rugăm să încerci din nou.\",\r\n        isUser: false\r\n      };\r\n      setMessages(prev => [...prev, errorResponse]);\r\n    } finally {\r\n      stopTimer();\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-darker-surface to-dark-surface\">\r\n      {/* Header */}\r\n      <header className=\"border-b border-dark-surface bg-darker-surface/80 backdrop-blur-sm\">\r\n        <div className=\"container mx-auto px-4 py-4 sm:py-6\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <h1 className=\"text-2xl sm:text-3xl font-bold gradient-text\">\r\n              AIpariat\r\n            </h1>\r\n            <div className=\"flex items-center space-x-2 text-xs sm:text-sm text-gray-400\">\r\n              <span className=\"w-2 h-2 bg-primary rounded-full animate-pulse\"></span>\r\n              <span className=\"hidden sm:inline\">AI Activ</span>\r\n              <span className=\"sm:hidden\">AI</span>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-gray-300 mt-2 text-sm sm:text-base\">\r\n            Toate casele de pariuri folosesc algoritmi pentru garantarea profitului. Am creat AIpariat pentru egalizarea șanselor.\r\n          </p>\r\n        </div>\r\n      </header>\r\n\r\n      <div className=\"container mx-auto px-4 py-4 sm:py-8 max-w-6xl\">\r\n        <div className=\"grid lg:grid-cols-3 gap-6 lg:gap-8\">\r\n          {/* Predefined Buttons */}\r\n          <div className=\"lg:col-span-1 order-2 lg:order-1\">\r\n            <h2 className=\"text-lg sm:text-xl font-semibold mb-4 sm:mb-6 text-primary\">\r\n              Bilete Rapide\r\n            </h2>\r\n            <div className=\"space-y-3 sm:space-y-4\">\r\n              <button\r\n                onClick={() => handlePredefinedButton('curajos')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-casino-red to-secondary rounded-lg border border-casino-red/30 hover:border-casino-red/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">🔥 Cel mai curajos bilet</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Cote mari, risc ridicat</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('sigur')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-casino-green to-primary rounded-lg border border-casino-green/30 hover:border-casino-green/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">🛡️ Cel mai sigur bilet</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Cote mici, șanse mari</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('ziua')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-ai-blue to-ai-purple rounded-lg border border-ai-blue/30 hover:border-ai-blue/60 transition-all duration-300 glow-ai hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-white text-sm sm:text-base\">⭐ Recomandarea zilei</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-200 mt-1\">Selecția AI pentru astăzi</div>\r\n                </div>\r\n              </button>\r\n\r\n              <button\r\n                onClick={() => handlePredefinedButton('saptamana')}\r\n                disabled={isLoading}\r\n                className=\"w-full p-3 sm:p-4 bg-gradient-to-r from-accent to-secondary rounded-lg border border-accent/30 hover:border-accent/60 transition-all duration-300 glow-primary hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed active:scale-95\"\r\n              >\r\n                <div className=\"text-left\">\r\n                  <div className=\"font-semibold text-black text-sm sm:text-base\">👑 Recomandarea săptămânii</div>\r\n                  <div className=\"text-xs sm:text-sm text-gray-800 mt-1\">Biletul premium al săptămânii</div>\r\n                </div>\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Chat Interface */}\r\n          <div\r\n            ref={chatContainerRef}\r\n            className={`lg:col-span-2 order-1 lg:order-2 transition-all duration-300 ${\r\n              isFullscreen\r\n                ? 'fixed inset-0 z-50 bg-gradient-to-br from-background via-darker-surface to-dark-surface p-4 flex flex-col'\r\n                : ''\r\n            }`}\r\n          >\r\n            {isFullscreen ? (\r\n              // Fullscreen Header\r\n              <div className=\"flex items-center justify-between mb-6 flex-shrink-0 border-b border-primary/20 pb-4\">\r\n                <div className=\"flex items-center space-x-3\">\r\n                  <h1 className=\"text-2xl font-bold gradient-text\">AIpariat</h1>\r\n                  <div className=\"flex items-center space-x-2 text-xs text-gray-400\">\r\n                    <span className=\"w-2 h-2 bg-primary rounded-full animate-pulse\"></span>\r\n                    <span>AI Chat</span>\r\n                  </div>\r\n                </div>\r\n                <button\r\n                  onClick={exitFullscreen}\r\n                  className=\"text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-800 transition-colors flex items-center space-x-2\"\r\n                  aria-label=\"Ieși din fullscreen\"\r\n                >\r\n                  <span className=\"text-sm\">Ieși</span>\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            ) : (\r\n              // Normal Header\r\n              <div className=\"flex items-center justify-between mb-4 sm:mb-6\">\r\n                <h2 className=\"text-lg sm:text-xl font-semibold text-primary\">\r\n                  Chat cu AI-ul nostru\r\n                </h2>\r\n              </div>\r\n            )}\r\n\r\n            {/* Chat Messages */}\r\n            <div className={`bg-dark-surface rounded-lg border border-primary/20 overflow-y-auto p-3 sm:p-4 mb-4 ${\r\n              isFullscreen\r\n                ? 'flex-1 h-full'\r\n                : 'h-64 sm:h-80 lg:h-96'\r\n            }`}>\r\n              {messages.length === 0 ? (\r\n                <div className=\"flex items-center justify-center h-full text-gray-400\">\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-4xl mb-4\">🤖</div>\r\n                    <p>Salut! Sunt AIpariat, expertul în pariuri sportive care folosește cele mai avansate analize AI.</p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"space-y-4\">\r\n                  {messages.map((message) => (\r\n                    <div\r\n                      key={message.id}\r\n                      className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}\r\n                    >\r\n                      <div\r\n                        className={`max-w-[280px] sm:max-w-xs lg:max-w-md px-3 sm:px-4 py-2 rounded-lg ${\r\n                          message.isUser\r\n                            ? 'bg-primary text-black'\r\n                            : 'bg-darker-surface text-white border border-primary/30'\r\n                        }`}\r\n                      >\r\n                        <div className=\"whitespace-pre-line text-sm\">\r\n                          {message.text}\r\n                        </div>\r\n                        {!message.isUser && message.shareUrl && (\r\n                          <div className=\"mt-3 pt-3 border-t border-primary/20\">\r\n                            <button\r\n                              onClick={() => copyToClipboard(message.shareUrl!)}\r\n                              className=\"flex items-center space-x-2 text-xs bg-primary/20 hover:bg-primary/30 px-3 py-1 rounded transition-colors\"\r\n                            >\r\n                              <span>🔗</span>\r\n                              <span>Partajează biletul</span>\r\n                            </button>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                  {isLoading && (\r\n                    <div className=\"flex justify-start\">\r\n                      <div className=\"bg-darker-surface text-white border border-primary/30 px-4 py-3 rounded-lg max-w-[320px] sm:max-w-sm\">\r\n                        {/* Timer and Progress Bar */}\r\n                        <div className=\"flex items-center justify-between mb-3\">\r\n                          <div className=\"flex items-center space-x-2\">\r\n                            <div className=\"w-3 h-3 bg-primary rounded-full animate-pulse\"></div>\r\n                            <span className=\"text-xs font-medium text-primary\">\r\n                              {Math.floor(loadingTime / 60)}:{(loadingTime % 60).toString().padStart(2, '0')}\r\n                            </span>\r\n                          </div>\r\n                          <div className=\"text-xs text-gray-400\">\r\n                            AI lucrează...\r\n                          </div>\r\n                        </div>\r\n\r\n                        {/* Progress Bar */}\r\n                        <div className=\"w-full bg-gray-700 rounded-full h-1.5 mb-3\">\r\n                          <div\r\n                            className=\"bg-gradient-to-r from-primary to-secondary h-1.5 rounded-full transition-all duration-1000 ease-out\"\r\n                            style={{\r\n                              width: `${Math.min((loadingTime / 30) * 100, 95)}%`\r\n                            }}\r\n                          ></div>\r\n                        </div>\r\n\r\n                        {/* Loading Message */}\r\n                        <div className=\"text-sm\">\r\n                          {loadingMessage}\r\n                        </div>\r\n\r\n                        {/* Animated Dots */}\r\n                        <div className=\"flex items-center space-x-1 mt-2\">\r\n                          <div className=\"w-1.5 h-1.5 bg-primary rounded-full animate-bounce\"></div>\r\n                          <div className=\"w-1.5 h-1.5 bg-primary rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\r\n                          <div className=\"w-1.5 h-1.5 bg-primary rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\r\n                          <div className=\"w-1.5 h-1.5 bg-primary rounded-full animate-bounce\" style={{animationDelay: '0.3s'}}></div>\r\n                        </div>\r\n\r\n                        {/* Helpful tip after 15 seconds */}\r\n                        {loadingTime > 15 && (\r\n                          <div className=\"mt-3 pt-3 border-t border-primary/20 text-xs text-gray-400\">\r\n                            💡 AI-ul folosește date în timp real pentru cele mai precise predicții\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n              <div ref={messagesEndRef} />\r\n            </div>\r\n\r\n            {/* Chat Input */}\r\n            <div className={`flex flex-col sm:flex-row gap-2 sm:gap-2 ${isFullscreen ? 'flex-shrink-0' : ''}`}>\r\n              <input\r\n                type=\"text\"\r\n                value={inputValue}\r\n                onChange={(e) => setInputValue(e.target.value)}\r\n                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}\r\n                onFocus={enterFullscreen}\r\n                placeholder=\"Descrie biletul dorit\"\r\n                disabled={isLoading}\r\n                className=\"flex-1 px-3 sm:px-4 py-3 bg-darker-surface border border-primary/30 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary disabled:opacity-50 text-sm sm:text-base\"\r\n              />\r\n              <button\r\n                onClick={handleSendMessage}\r\n                disabled={isLoading || !inputValue.trim()}\r\n                className=\"px-4 sm:px-6 py-3 bg-primary text-black font-semibold rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base whitespace-nowrap\"\r\n              >\r\n                Trimite\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <footer className=\"mt-8 sm:mt-16 text-center text-gray-400 text-xs sm:text-sm\">\r\n          <div className=\"border-t border-dark-surface pt-6 sm:pt-8\">\r\n            <p>&copy; 2025 AIpariat. Toate drepturile rezervate.</p>\r\n            <p className=\"mt-2\">\r\n              Această platformă are exclusiv rol consultativ și nu poate fi folosită pentru plasarea pariurilor sportive.\r\n            </p>\r\n          </div>\r\n        </footer>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAae,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAC/C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEhD,yBAAyB;IACzB,MAAM,aAAa,CAAC;QAClB,eAAe;QACf,kBAAkB;QAElB,SAAS,OAAO,GAAG,YAAY;YAC7B,eAAe,CAAA;gBACb,MAAM,UAAU,OAAO;gBAEvB,+CAA+C;gBAC/C,IAAI,WAAW,GAAG;oBAChB,kBAAkB;gBACpB,OAAO,IAAI,WAAW,IAAI;oBACxB,kBAAkB;gBACpB,OAAO,IAAI,WAAW,IAAI;oBACxB,kBAAkB;gBACpB,OAAO,IAAI,WAAW,IAAI;oBACxB,kBAAkB;gBACpB,OAAO,IAAI,WAAW,IAAI;oBACxB,kBAAkB;gBACpB,OAAO;oBACL,kBAAkB;gBACpB;gBAEA,OAAO;YACT;QACF,GAAG;IACL;IAEA,MAAM,YAAY;QAChB,IAAI,SAAS,OAAO,EAAE;YACpB,cAAc,SAAS,OAAO;YAC9B,SAAS,OAAO,GAAG;QACrB;QACA,eAAe;QACf,kBAAkB;IACpB;IAEA,6CAA6C;IAC7C,MAAM,iBAAiB;QACrB,4EAA4E;QAC5E,WAAW;YACT,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAC9D,GAAG;IACL;IAEA,gEAAgE;IAChE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;QACF;yBAAG;QAAC;QAAU;QAAW;KAAa;IAEtC,4BAA4B;IAC5B,MAAM,kBAAkB;QACtB,6EAA6E;QAC7E,MAAM,WAAW,OAAO,UAAU,IAAI,OACrB,iEAAiE,IAAI,CAAC,UAAU,SAAS;QAE1G,IAAI,UAAU;YACZ,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YAE/B,yEAAyE;YACzE,WAAW;gBACT,iBAAiB,OAAO,EAAE,eAAe;oBACvC,UAAU;oBACV,OAAO;gBACT;YACF,GAAG;QACL;IACF;IAEA,MAAM,iBAAiB;QACrB,QAAQ,GAAG,CAAC;QACZ,gBAAgB;QAChB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;IACjC;IAEA,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;kCAAO;oBACL,IAAI,SAAS,OAAO,EAAE;wBACpB,cAAc,SAAS,OAAO;oBAChC;oBACA,gCAAgC;oBAChC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;yBAAG,EAAE;IAEL,oCAAoC;IACpC,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,MAAM,EAAE;YACtE;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,yCAAyC;YACzC,MAAM,UAAU,KAAK,OAAO;YAC5B,IAAI,SAAS;gBACX,aAAa,OAAO,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,SAAS,CAAC;oBACvD;oBACA,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,SAAS;gBACnF;YACF;YAEA,OAAO,KAAK,QAAQ;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAE3C,+CAA+C;YAC/C,MAAM,WAAW,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACjF,aAAa,OAAO,CAAC,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,SAAS,CAAC;gBACxD;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK,MAAM,WAAW;gBACrE,WAAW;YACb;YAEA,OAAO,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,UAAU;QACtD;IACF;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,oBAAoB;YAClC,8BAA8B;YAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,MAAM;YACf,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,6BAA6B;QAC7B;QAEA,MAAM,cAAc;YAClB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,QAAQ;QACV;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,MAAM,YAAY;QAClB,cAAc;QACd,aAAa;QACb,WAAW;QAEX,4CAA4C;QAC5C,IAAI,cAAc;YAChB,WAAW;gBACT;YACF,GAAG;QACL;QAEA,IAAI;YACF,iCAAiC;YACjC,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE;YACxC,MAAM,WAAW,MAAM,iBAAiB;YACxC,MAAM,aAAa;gBACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,QAAQ;gBACR,UAAU,OAAO,EAAE;gBACnB,UAAU;YACZ;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,gBAAgB;gBACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,QAAQ;YACV;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAc;QAC9C,SAAU;YACR;YACA,aAAa;QACf;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,6BAA6B;QAC7B;QAEA,wDAAwD;QACxD,MAAM,uBAAuB,CAAC;YAC5B,OAAQ;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,6BAA6B;QAC7B,MAAM,gBAAgB;YACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM,CAAC,aAAa,EAAE,qBAAqB,OAAO;YAClD,QAAQ;QACV;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAc;QAE5C,aAAa;QAEb,kDAAkD;QAClD,MAAM,kBAAkB,CAAC;YACvB,OAAQ;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,OAAO;YACX;QACF;QAEA,WAAW,gBAAgB;QAE3B,4CAA4C;QAC5C,IAAI,cAAc;YAChB,WAAW;gBACT;YACF,GAAG;QACL;QAEA,IAAI;YACF,iCAAiC;YACjC,MAAM,SAAS,MAAM,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD,EAAE;YAC9C,MAAM,WAAW,MAAM,iBAAiB;YACxC,MAAM,WAAW;gBACf,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,QAAQ;gBACR,UAAU,OAAO,EAAE;gBACnB,UAAU;YACZ;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAS;QACzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM,gBAAgB;gBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,QAAQ;YACV;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAc;QAC9C,SAAU;YACR;YACA,aAAa;QACf;IACF;IAIA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAG7D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,6LAAC;4CAAK,WAAU;sDAAY;;;;;;;;;;;;;;;;;;sCAGhC,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAM3D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAG3E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,6LAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,6LAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;0DAI3D,6LAAC;gDACC,SAAS,IAAM,uBAAuB;gDACtC,UAAU;gDACV,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgD;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;sEAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/D,6LAAC;gCACC,KAAK;gCACL,WAAW,CAAC,6DAA6D,EACvE,eACI,8GACA,IACJ;;oCAED,eACC,oBAAoB;kDACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;;;;;0EAChB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,6LAAC;gDACC,SAAS;gDACT,WAAU;gDACV,cAAW;;kEAEX,6LAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;+CAK3E,gBAAgB;kDAChB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAG,WAAU;sDAAgD;;;;;;;;;;;kDAOlE,6LAAC;wCAAI,WAAW,CAAC,oFAAoF,EACnG,eACI,kBACA,wBACJ;;4CACC,SAAS,MAAM,KAAK,kBACnB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,6LAAC;sEAAE;;;;;;;;;;;;;;;;qEAIP,6LAAC;gDAAI,WAAU;;oDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4DAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,gBAAgB,iBAAiB;sEAErE,cAAA,6LAAC;gEACC,WAAW,CAAC,mEAAmE,EAC7E,QAAQ,MAAM,GACV,0BACA,yDACJ;;kFAEF,6LAAC;wEAAI,WAAU;kFACZ,QAAQ,IAAI;;;;;;oEAEd,CAAC,QAAQ,MAAM,IAAI,QAAQ,QAAQ,kBAClC,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EACC,SAAS,IAAM,gBAAgB,QAAQ,QAAQ;4EAC/C,WAAU;;8FAEV,6LAAC;8FAAK;;;;;;8FACN,6LAAC;8FAAK;;;;;;;;;;;;;;;;;;;;;;;2DApBT,QAAQ,EAAE;;;;;oDA2BlB,2BACC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EAEb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;;;;;8FACf,6LAAC;oFAAK,WAAU;;wFACb,KAAK,KAAK,CAAC,cAAc;wFAAI;wFAAE,CAAC,cAAc,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;;;sFAG9E,6LAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAMzC,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEACC,WAAU;wEACV,OAAO;4EACL,OAAO,GAAG,KAAK,GAAG,CAAC,AAAC,cAAc,KAAM,KAAK,IAAI,CAAC,CAAC;wEACrD;;;;;;;;;;;8EAKJ,6LAAC;oEAAI,WAAU;8EACZ;;;;;;8EAIH,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC;4EAAI,WAAU;4EAAqD,OAAO;gFAAC,gBAAgB;4EAAM;;;;;;sFAClG,6LAAC;4EAAI,WAAU;4EAAqD,OAAO;gFAAC,gBAAgB;4EAAM;;;;;;sFAClG,6LAAC;4EAAI,WAAU;4EAAqD,OAAO;gFAAC,gBAAgB;4EAAM;;;;;;;;;;;;gEAInG,cAAc,oBACb,6LAAC;oEAAI,WAAU;8EAA6D;;;;;;;;;;;;;;;;;;;;;;;0DASxF,6LAAC;gDAAI,KAAK;;;;;;;;;;;;kDAIZ,6LAAC;wCAAI,WAAW,CAAC,yCAAyC,EAAE,eAAe,kBAAkB,IAAI;;0DAC/F,6LAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;gDACxC,SAAS;gDACT,aAAY;gDACZ,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC;gDACC,SAAS;gDACT,UAAU,aAAa,CAAC,WAAW,IAAI;gDACvC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAE,WAAU;8CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;GA9hBwB;KAAA", "debugId": null}}, {"offset": {"line": 1276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Projects/NewAipariat/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}